/// 바라 부스 매니저 - 나이스 페이 빌링 서비스
///
/// 나이스 페이 for start 빌링 API를 사용한 정기구독 결제 서비스입니다.
/// - 빌링키 발급 (비인증)
/// - 빌링 승인 (정기결제)
/// - 빌링키 삭제
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'dart:convert';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import '../utils/logger_utils.dart';

/// 나이스 페이 빌링 서비스
class NicePayService {
  static const String _tag = 'NicePayService';
  
  // 테스트 환경 설정 (나이스페이 공식 문서 기준)
  static const String _baseUrl = 'https://webapi.nicepay.co.kr/webapi';
  static const String _merchantKey = 'b+zhZ4yOZ7FsH8pm5lhDfHZEb79tIwnjsdA0FBXh86yLc6BJeFVrZFXhAoJ3gEWgrWwN+lJMV0W4hvDdbe4Sjw==';
  static const String _mid = 'nictest04m'; // 테스트 가맹점 ID

  // 신 버전 API용 설정
  static const String _secretKey = 'b+zhZ4yOZ7FsH8pm5lhDfHZEb79tIwnjsdA0FBXh86yLc6BJeFVrZFXhAoJ3gEWgrWwN+lJMV0W4hvDdbe4Sjw==';
  static const String _credentials = 'bmljdGVzdDA0bTpiK3poWjR5T1o3RnNIOHBtNWxoRGZIWkViNzl0SXduanNkQTBGQlhoODZ5TGM2QkplRlZyWkZYaEFvSjNnRVdnclc='; // Base64(MID:SecretKey)
  
  /// 빌링키 발급 (비인증)
  Future<NiceBillingKeyResult> issueBillingKey({
    required String cardNo,
    required String expYear,
    required String expMonth,
    required String idNo,
    required String cardPw,
    required String customerName,
    required String customerEmail,
    required String customerTel,
  }) async {
    try {
      final moid = _generateMoid();
      final ediDate = _getEdiDate();
      
      LoggerUtils.logInfo('빌링키 발급 시작: $moid', tag: _tag);
      
      // 카드 정보 암호화
      final cardData = 'CardNo=$cardNo&ExpYear=$expYear&ExpMonth=$expMonth&IDNo=$idNo&CardPw=$cardPw';
      final encData = _encryptAES(cardData, _merchantKey.substring(0, 16));

      // 위변조 검증 데이터 생성
      final signData = _generateSignData('$_mid$ediDate$moid$_merchantKey');
      
      final url = Uri.parse('$_baseUrl/billing/billing_regist.jsp');
      final headers = {
        'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8',
        'Accept': 'application/json',
      };

      final body = {
        'MID': _mid,
        'EdiDate': ediDate,
        'Moid': moid,
        'EncData': encData,
        'SignData': signData,
        'BuyerName': customerName,
        'BuyerEmail': customerEmail,
        'BuyerTel': customerTel,
        'CharSet': 'utf-8',
        'EdiType': 'JSON',
      };

      LoggerUtils.logInfo('빌링키 발급 요청: $body', tag: _tag);

      final response = await http.post(
        url,
        headers: headers,
        body: body,
      );
      
      LoggerUtils.logInfo('빌링키 발급 응답: ${response.statusCode}', tag: _tag);
      LoggerUtils.logInfo('빌링키 발급 응답 본문: ${response.body}', tag: _tag);
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        
        if (data['ResultCode'] == 'F100') {
          // 성공
          return NiceBillingKeyResult(
            success: true,
            billingKey: data['BID'],
            tid: data['TID'],
            cardInfo: NiceCardInfo(
              cardCode: data['CardCode'] ?? '',
              cardName: data['CardName'] ?? '',
              cardCl: data['CardCl'] ?? '',
              acquCardCode: data['AcquCardCode'] ?? '',
              acquCardName: data['AcquCardName'] ?? '',
            ),
            authDate: data['AuthDate'],
          );
        } else {
          // 실패
          return NiceBillingKeyResult(
            success: false,
            errorCode: data['ResultCode'],
            errorMessage: data['ResultMsg'],
          );
        }
      } else {
        return NiceBillingKeyResult(
          success: false,
          errorMessage: 'HTTP 오류: ${response.statusCode}',
        );
      }
    } catch (e) {
      LoggerUtils.logError('빌링키 발급 중 오류', tag: _tag, error: e);
      return NiceBillingKeyResult(
        success: false,
        errorMessage: '빌링키 발급 중 오류가 발생했습니다: ${e.toString()}',
      );
    }
  }
  
  /// 빌링 승인 (정기결제)
  Future<NicePaymentResult> executePayment({
    required String billingKey,
    required String tid,
    required int amount,
    required String goodsName,
    String? orderId,
  }) async {
    try {
      final moid = orderId ?? _generateMoid();
      final ediDate = _getEdiDate();
      final newTid = _generateTid(); // 새로운 TID 생성 (중복 방지)
      
      LoggerUtils.logInfo('빌링 승인 시작: $moid, 금액: $amount', tag: _tag);
      
      // 위변조 검증 데이터 생성 (나이스페이 공식 문서 순서)
      final amountStr = amount.toString().padLeft(12, '0');
      final signData = _generateSignData('$_mid$ediDate$moid$amountStr$billingKey$_merchantKey');
      
      final url = Uri.parse('$_baseUrl/billing/billing_approve.jsp');
      final headers = {
        'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8',
        'Accept': 'application/json',
      };

      final body = {
        'BID': billingKey,
        'MID': _mid,
        'TID': newTid,
        'EdiDate': ediDate,
        'Moid': moid,
        'Amt': amountStr,
        'GoodsName': goodsName,
        'SignData': signData,
        'CardInterest': '0', // 무이자 미사용
        'CardQuota': '00', // 일시불
        'CharSet': 'utf-8',
        'EdiType': 'JSON',
        'BuyerName': '구매자',
        'BuyerEmail': '<EMAIL>',
        'BuyerTel': '01012345678',
      };

      LoggerUtils.logInfo('빌링 승인 요청: $body', tag: _tag);

      final response = await http.post(
        url,
        headers: headers,
        body: body,
      );
      
      LoggerUtils.logInfo('빌링 승인 응답: ${response.statusCode}', tag: _tag);
      LoggerUtils.logInfo('빌링 승인 응답 본문: ${response.body}', tag: _tag);
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        
        if (data['ResultCode'] == '3001') {
          // 성공
          return NicePaymentResult(
            success: true,
            tid: data['TID'],
            moid: data['Moid'],
            amount: int.tryParse(data['Amt']?.toString() ?? '0') ?? 0,
            authCode: data['AuthCode'],
            authDate: data['AuthDate'],
            cardNo: data['CardNo'],
            cardName: data['CardName'],
          );
        } else {
          // 실패
          return NicePaymentResult(
            success: false,
            errorCode: data['ResultCode'],
            errorMessage: data['ResultMsg'],
          );
        }
      } else {
        return NicePaymentResult(
          success: false,
          errorMessage: 'HTTP 오류: ${response.statusCode}',
        );
      }
    } catch (e) {
      LoggerUtils.logError('빌링 승인 중 오류', tag: _tag, error: e);
      return NicePaymentResult(
        success: false,
        errorMessage: '빌링 승인 중 오류가 발생했습니다: ${e.toString()}',
      );
    }
  }
  
  /// 빌링키 삭제
  Future<bool> deleteBillingKey(String billingKey) async {
    try {
      final moid = _generateMoid();
      final ediDate = _getEdiDate();
      
      LoggerUtils.logInfo('빌링키 삭제 시작: $billingKey', tag: _tag);
      
      // 위변조 검증 데이터 생성
      final signData = _generateSignData('$_mid$ediDate$moid$billingKey$_merchantKey');
      
      final url = Uri.parse('$_baseUrl/billing/billkey_remove.jsp');
      final headers = {
        'Content-Type': 'application/x-www-form-urlencoded; charset=utf-8',
        'Accept': 'application/json',
      };

      final body = {
        'BID': billingKey,
        'MID': _mid,
        'EdiDate': ediDate,
        'Moid': moid,
        'SignData': signData,
        'CharSet': 'utf-8',
        'EdiType': 'JSON',
      };

      final response = await http.post(
        url,
        headers: headers,
        body: body,
      );
      
      LoggerUtils.logInfo('빌링키 삭제 응답: ${response.statusCode}', tag: _tag);
      LoggerUtils.logInfo('빌링키 삭제 응답 본문: ${response.body}', tag: _tag);
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['ResultCode'] == 'F101';
      }
      
      return false;
    } catch (e) {
      LoggerUtils.logError('빌링키 삭제 중 오류', tag: _tag, error: e);
      return false;
    }
  }
  
  /// API 키 유효성 테스트
  Future<NiceApiKeyTestResult> testApiKeys() async {
    try {
      LoggerUtils.logInfo('API 키 유효성 테스트 시작', tag: _tag);
      
      // 더미 데이터로 빌링키 발급 테스트 (실제 발급하지 않음)
      final result = await issueBillingKey(
        cardNo: '****************', // 테스트 카드
        expYear: '25',
        expMonth: '12',
        idNo: '800101',
        cardPw: '00',
        customerName: '테스트',
        customerEmail: '<EMAIL>',
        customerTel: '01012345678',
      );
      
      if (result.success) {
        return NiceApiKeyTestResult(
          isValid: true,
          message: 'API 키가 유효합니다! ✅',
        );
      } else {
        return NiceApiKeyTestResult(
          isValid: false,
          errorCode: result.errorCode,
          message: result.errorMessage ?? 'API 키 테스트 실패',
        );
      }
    } catch (e) {
      LoggerUtils.logError('API 키 테스트 중 오류', tag: _tag, error: e);
      return NiceApiKeyTestResult(
        isValid: false,
        message: 'API 키 테스트 중 오류가 발생했습니다: ${e.toString()}',
      );
    }
  }
  
  /// 전문생성일시 생성 (YYYYMMDDHHMMSS)
  String _getEdiDate() {
    return DateFormat('yyyyMMddHHmmss').format(DateTime.now());
  }
  
  /// 주문번호 생성
  String _generateMoid() {
    return 'PARA_${DateTime.now().millisecondsSinceEpoch}';
  }
  
  /// TID 생성 (나이스페이 빌링 승인용 - 발급 TID 형식 참고)
  String _generateTid() {
    final now = DateTime.now();
    final dateStr = DateFormat('yyMMddHHmmss').format(now);
    final random = (DateTime.now().millisecondsSinceEpoch % 10000).toString().padLeft(4, '0');
    // 빌링키 발급 시 받은 TID 형식: nictest04m0116250819000323C072
    // 빌링 승인용 TID도 비슷한 형식으로: MID + 0116 + 날짜시간 + 랜덤번호
    return '${_mid}0116$dateStr$random';
  }
  
  /// SHA-256 해시 생성
  String _generateSignData(String data) {
    LoggerUtils.logInfo('SHA-256 해시 생성 - data: $data', tag: _tag);
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    final result = digest.toString();
    LoggerUtils.logInfo('SHA-256 해시 결과 - result: $result', tag: _tag);
    return result;
  }

  /// 결제 취소 (신 버전 API)
  Future<Map<String, dynamic>> cancelPayment({
    required String tid,
    required String orderId,
    required String reason,
    int? cancelAmt, // null이면 전액취소
  }) async {
    try {
      LoggerUtils.logInfo('결제 취소 시작: TID=$tid, 사유=$reason', tag: _tag);

      // ediDate 생성
      final now = DateTime.now();
      final ediDate = DateFormat('yyyyMMddHHmmss').format(now);

      // SignData 생성: hex(sha256(tid + ediDate + SecretKey))
      final signDataInput = '$tid$ediDate$_secretKey';
      final signData = _generateSignData(signDataInput);

      LoggerUtils.logInfo('취소 SignData 생성 완료', tag: _tag);

      // 요청 데이터 구성
      final requestData = <String, dynamic>{
        'reason': reason,
        'orderId': orderId,
        'ediDate': ediDate,
        'signData': signData,
        'returnCharSet': 'utf-8',
      };

      // 부분취소인 경우 금액 추가
      if (cancelAmt != null) {
        requestData['cancelAmt'] = cancelAmt.toString();
      }

      LoggerUtils.logInfo('결제 취소 요청: $requestData', tag: _tag);

      // API 호출 (신 버전 API)
      final response = await http.post(
        Uri.parse('https://api.nicepay.co.kr/v1/payments/$tid/cancel'),
        headers: {
          'Content-Type': 'application/json; charset=utf-8',
          'Authorization': 'Basic $_credentials',
        },
        body: json.encode(requestData),
      );

      LoggerUtils.logInfo('결제 취소 응답: ${response.statusCode}', tag: _tag);
      LoggerUtils.logInfo('결제 취소 응답 본문: ${response.body}', tag: _tag);

      final responseData = json.decode(response.body);

      if (responseData['resultCode'] == '0000') {
        LoggerUtils.logInfo('결제 취소 성공: ${responseData['resultMsg']}', tag: _tag);
        return {
          'success': true,
          'data': responseData,
        };
      } else {
        LoggerUtils.logError('결제 취소 실패: ${responseData['resultMsg']}', tag: _tag);
        return {
          'success': false,
          'error': responseData['resultMsg'],
          'data': responseData,
        };
      }
    } catch (e) {
      LoggerUtils.logError('결제 취소 오류: $e', tag: _tag);
      return {
        'success': false,
        'error': '결제 취소 중 오류가 발생했습니다: $e',
      };
    }
  }
  
  /// AES-128-ECB 암호화 (나이스페이 공식 방식)
  String _encryptAES(String plainText, String key) {
    try {
      LoggerUtils.logInfo('AES 암호화 시작 - plainText: $plainText, key: ${key.substring(0, 16)}', tag: _tag);

      // 나이스페이 공식 문서: MerchantKey의 앞 16자리를 그대로 사용
      final aesKey = key.substring(0, 16);
      final keyBytes = utf8.encode(aesKey);
      final key128 = Key(Uint8List.fromList(keyBytes));

      // AES-128-ECB 모드로 암호화
      final encrypter = Encrypter(AES(key128, mode: AESMode.ecb));
      final encrypted = encrypter.encrypt(plainText);

      // Hex 인코딩으로 반환 (대문자)
      final result = encrypted.base16.toUpperCase();
      LoggerUtils.logInfo('AES 암호화 완료 - result: $result', tag: _tag);
      return result;
    } catch (e) {
      LoggerUtils.logError('AES 암호화 오류', tag: _tag, error: e);
      throw Exception('AES 암호화 실패: ${e.toString()}');
    }
  }
}

/// 나이스 페이 빌링키 발급 결과
class NiceBillingKeyResult {
  final bool success;
  final String? billingKey;
  final String? tid;
  final NiceCardInfo? cardInfo;
  final String? authDate;
  final String? errorCode;
  final String? errorMessage;

  NiceBillingKeyResult({
    required this.success,
    this.billingKey,
    this.tid,
    this.cardInfo,
    this.authDate,
    this.errorCode,
    this.errorMessage,
  });
}

/// 나이스 페이 결제 결과
class NicePaymentResult {
  final bool success;
  final String? tid;
  final String? moid;
  final int? amount;
  final String? authCode;
  final String? authDate;
  final String? cardNo;
  final String? cardName;
  final String? errorCode;
  final String? errorMessage;

  NicePaymentResult({
    required this.success,
    this.tid,
    this.moid,
    this.amount,
    this.authCode,
    this.authDate,
    this.cardNo,
    this.cardName,
    this.errorCode,
    this.errorMessage,
  });
}

/// 나이스 페이 카드 정보
class NiceCardInfo {
  final String cardCode;
  final String cardName;
  final String cardCl; // 0: 신용카드, 1: 체크카드
  final String acquCardCode;
  final String acquCardName;

  NiceCardInfo({
    required this.cardCode,
    required this.cardName,
    required this.cardCl,
    required this.acquCardCode,
    required this.acquCardName,
  });

  String get cardTypeText => cardCl == '0' ? '신용카드' : '체크카드';
}

/// 나이스 페이 API 키 테스트 결과
class NiceApiKeyTestResult {
  final bool isValid;
  final String? errorCode;
  final String message;

  NiceApiKeyTestResult({
    required this.isValid,
    this.errorCode,
    required this.message,
  });
}


