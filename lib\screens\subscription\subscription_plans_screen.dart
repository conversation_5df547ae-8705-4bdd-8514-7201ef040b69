/// 바라 부스 매니저 - 구독 플랜 비교 화면
///
/// 무료, 플러스, 프로 플랜의 기능을 비교하고 업그레이드를 안내하는 화면입니다.
/// - 플랜별 기능 비교표
/// - 가격 정보
/// - 업그레이드 안내
///
/// 작성자: Blue
/// 버전: 2.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/subscription_plan.dart';
import '../../providers/subscription_provider.dart';
import '../../widgets/app_bar_styles.dart';
import '../../utils/currency_utils.dart';
import '../../utils/toast_utils.dart';
import 'subscription_management_screen.dart';

/// 구독 플랜 비교 화면
class SubscriptionPlansScreen extends ConsumerStatefulWidget {
  const SubscriptionPlansScreen({super.key});

  @override
  ConsumerState<SubscriptionPlansScreen> createState() => _SubscriptionPlansScreenState();
}

class _SubscriptionPlansScreenState extends ConsumerState<SubscriptionPlansScreen> {
  bool _isUpgrading = false;

  @override
  Widget build(BuildContext context) {
    final currentPlanAsync = ref.watch(currentPlanTypeProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: Builder(builder: (ctx) => Text('플랜 비교', style: AppBarStyles.of(ctx))),
        centerTitle: true,
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
      ),
      body: SafeArea(
        child: currentPlanAsync.when(
          data: (currentPlanType) => _buildContent(currentPlanType),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error, size: 64, color: Colors.red),
                SizedBox(height: 16),
                Text('플랜 정보를 불러올 수 없습니다'),
                SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => ref.refresh(currentPlanTypeProvider),
                  child: Text('다시 시도'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildContent(SubscriptionPlanType currentPlanType) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 헤더 섹션
          _buildHeaderSection(currentPlanType),
          const SizedBox(height: 32),
          
          // 플랜 카드들
          _buildPlanCards(currentPlanType),
          const SizedBox(height: 32),
          
          // 기능 비교표
          _buildFeatureComparison(),
          const SizedBox(height: 32),
          
          // FAQ 섹션
          _buildFaqSection(),
        ],
      ),
    );
  }

  Widget _buildHeaderSection(SubscriptionPlanType currentPlanType) {
    return Container(
      padding: const EdgeInsets.all(28),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.blue.shade50,
            Colors.indigo.shade50,
            Colors.purple.shade50,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: _getPlanColor(currentPlanType).withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  _getPlanIcon(currentPlanType),
                  color: _getPlanColor(currentPlanType),
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '현재 플랜',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _getPlanDisplayName(currentPlanType),
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.w700,
                        color: _getPlanColor(currentPlanType),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.7),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.white.withValues(alpha: 0.5)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.lightbulb_outline,
                  color: Colors.amber.shade600,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    '더 많은 기능을 원하시나요? 플랜을 업그레이드하여 무제한으로 이용해보세요!',
                    style: TextStyle(
                      fontSize: 15,
                      color: Colors.grey.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlanCards(SubscriptionPlanType currentPlanType) {
    return Column(
      children: [
        // 첫 번째 행: 무료 플랜과 플러스 플랜
        Row(
          children: [
            // 무료 플랜 카드
            Expanded(
              child: _buildPlanCard(
                plan: PredefinedPlans.free,
                isCurrentPlan: currentPlanType == SubscriptionPlanType.free,
                isRecommended: false,
              ),
            ),
            const SizedBox(width: 16),
            // 플러스 플랜 카드
            Expanded(
              child: _buildPlanCard(
                plan: PredefinedPlans.plus,
                isCurrentPlan: currentPlanType == SubscriptionPlanType.plus,
                isRecommended: false,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        // 두 번째 행: 프로 플랜 (추천)
        _buildPlanCard(
          plan: PredefinedPlans.pro,
          isCurrentPlan: currentPlanType == SubscriptionPlanType.pro,
          isRecommended: true,
          isFullWidth: true,
        ),
      ],
    );
  }

  Widget _buildPlanCard({
    required SubscriptionPlan plan,
    required bool isCurrentPlan,
    required bool isRecommended,
    bool isFullWidth = false,
  }) {
    final planColor = _getPlanColor(plan.type);

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: isRecommended
            ? LinearGradient(
                colors: [
                  Colors.amber.shade50,
                  Colors.orange.shade50,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              )
            : isCurrentPlan
                ? LinearGradient(
                    colors: [
                      planColor.withValues(alpha: 0.05),
                      planColor.withValues(alpha: 0.1),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                : null,
        color: isRecommended || isCurrentPlan ? null : Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isRecommended
              ? Colors.amber.shade300
              : isCurrentPlan
                  ? planColor.withValues(alpha: 0.3)
                  : Colors.grey.shade200,
          width: isRecommended || isCurrentPlan ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isRecommended
                ? Colors.amber.withValues(alpha: 0.2)
                : isCurrentPlan
                    ? planColor.withValues(alpha: 0.15)
                    : Colors.grey.withValues(alpha: 0.1),
            blurRadius: isRecommended || isCurrentPlan ? 12 : 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 플랜 이름과 추천 배지
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: planColor.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  _getPlanIcon(plan.type),
                  color: planColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      plan.name,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w700,
                        color: planColor,
                      ),
                    ),
                    if (isCurrentPlan)
                      Text(
                        '현재 플랜',
                        style: TextStyle(
                          fontSize: 12,
                          color: planColor.withValues(alpha: 0.7),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                  ],
                ),
              ),
              if (isRecommended)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.amber.shade400, Colors.orange.shade400],
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.amber.withValues(alpha: 0.3),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.star,
                        size: 14,
                        color: Colors.white,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '추천',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
          
          // 플랜 설명
          Text(
            plan.description,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 16),
          
          // 가격
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: plan.isFree
                  ? Colors.green.withValues(alpha: 0.1)
                  : planColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: plan.isFree
                    ? Colors.green.withValues(alpha: 0.2)
                    : planColor.withValues(alpha: 0.2),
              ),
            ),
            child: plan.isFree
                ? Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: Colors.green,
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '무료',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.w700,
                          color: Colors.green,
                        ),
                      ),
                    ],
                  )
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.baseline,
                        textBaseline: TextBaseline.alphabetic,
                        children: [
                          Text(
                            CurrencyUtils.formatCurrency(plan.monthlyPrice),
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.w700,
                              color: planColor,
                            ),
                          ),
                          Text(
                            '/월',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: planColor.withValues(alpha: 0.7),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '연간 결제 시 ${CurrencyUtils.formatCurrency(plan.yearlyPrice)}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
          ),
          const SizedBox(height: 20),
          
          // 현재 플랜 표시 또는 업그레이드 버튼
          if (isCurrentPlan) ...[
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    planColor.withValues(alpha: 0.1),
                    planColor.withValues(alpha: 0.05),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: planColor.withValues(alpha: 0.3)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.check_circle,
                    color: planColor,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '현재 플랜',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: planColor,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          ] else if (plan.isPlus) ...[
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.blue.shade500, Colors.blue.shade600],
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.blue.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: ElevatedButton(
                onPressed: _isUpgrading ? null : () => _upgradeToPlus(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  shadowColor: Colors.transparent,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _isUpgrading
                    ? SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.upgrade, size: 20),
                          const SizedBox(width: 8),
                          Text(
                            '업그레이드',
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
              ),
            ),
          ] else if (plan.isPro) ...[
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.amber.shade400, Colors.orange.shade500],
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.amber.withValues(alpha: 0.4),
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                  ),
                ],
              ),
              child: ElevatedButton(
                onPressed: _isUpgrading ? null : () => _upgradeToPro(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  shadowColor: Colors.transparent,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _isUpgrading
                    ? SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.diamond, size: 20),
                          const SizedBox(width: 8),
                          Text(
                            '업그레이드',
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFeatureComparison() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.grey.shade50,
            Colors.white,
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.compare_arrows,
                color: Colors.blue.shade600,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                '기능 비교',
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.w700,
                  color: Colors.grey.shade800,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // 헤더 행
          Container(
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.blue.shade50,
                  Colors.indigo.shade50,
                ],
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.shade100),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    '기능',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w700,
                      color: Colors.grey.shade800,
                    ),
                  ),
                ),
                Expanded(
                  child: Column(
                    children: [
                      Icon(Icons.star_border, color: Colors.grey.shade600, size: 20),
                      const SizedBox(height: 4),
                      Text(
                        '무료',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    children: [
                      Icon(Icons.star_half, color: Colors.blue.shade600, size: 20),
                      const SizedBox(height: 4),
                      Text(
                        '플러스',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    children: [
                      Icon(Icons.star, color: Colors.amber.shade600, size: 20),
                      const SizedBox(height: 4),
                      Text(
                        '프로',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.amber.shade700,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),

          // 기능 비교 행들 - 체크(✅)가 많은 기능부터 정렬
          _buildFeatureRow('행사 등록', '1개', '무제한', '무제한'),
          _buildFeatureRow('상품 등록', '30개', '무제한', '무제한'),
          _buildFeatureRow('세트 할인', '❌', '✅', '✅'),
          _buildFeatureRow('엑셀 내보내기', '❌', '✅', '✅'),
          _buildFeatureRow('고급 통계', '❌', '✅', '✅'),
          _buildFeatureRow('서비스 기능', '❌', '✅', '✅'),
          _buildFeatureRow('실시간 동기화', '❌', '❌', '✅'),
          _buildFeatureRow('서버 연동', '❌', '❌', '✅'),
          _buildFeatureRow('판매자별 관리', '❌', '❌', '✅'),
          _buildFeatureRow('PDF 내보내기', '❌', '❌', '✅'),
          _buildFeatureRow('데이터 백업', '로컬만', '로컬만', '클라우드'),
        ],
      ),
    );
  }

  Widget _buildFeatureRow(String feature, String freeValue, String plusValue, String proValue) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade100),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              feature,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade800,
              ),
            ),
          ),
          Expanded(
            child: _buildFeatureValue(freeValue, Colors.grey.shade600),
          ),
          Expanded(
            child: _buildFeatureValue(plusValue, Colors.blue.shade600),
          ),
          Expanded(
            child: _buildFeatureValue(proValue, Colors.amber.shade600),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureValue(String value, Color baseColor) {
    if (value == '✅') {
      return Container(
        alignment: Alignment.center,
        child: Container(
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(
            Icons.check,
            color: Colors.green.shade600,
            size: 16,
          ),
        ),
      );
    } else if (value == '❌') {
      return Container(
        alignment: Alignment.center,
        child: Container(
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: Colors.red.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(
            Icons.close,
            color: Colors.red.shade600,
            size: 16,
          ),
        ),
      );
    } else {
      return Container(
        alignment: Alignment.center,
        child: Text(
          value,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 13,
            color: baseColor,
            fontWeight: FontWeight.w600,
          ),
        ),
      );
    }
  }

  Widget _buildFaqSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '자주 묻는 질문',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),

          _buildFaqItem(
            '무료 플랜에서 프로 플랜으로 언제든 업그레이드할 수 있나요?',
            '네, 언제든지 업그레이드할 수 있습니다. 기존 데이터는 모두 유지됩니다.',
          ),
          _buildFaqItem(
            '프로 플랜을 해지하면 어떻게 되나요?',
            '무료 플랜으로 자동 전환되며, 무료 플랜 제한에 맞게 데이터가 조정됩니다.',
          ),
          _buildFaqItem(
            '행사 리셋 기능이란 무엇인가요?',
            '행사는 유지하되 모든 데이터(상품, 판매기록 등)를 삭제하여 새로 시작하는 기능입니다.',
          ),
          _buildFaqItem(
            '데이터는 안전하게 보관되나요?',
            '프로 플랜에서는 클라우드 백업을 통해 데이터를 안전하게 보관합니다.',
          ),
        ],
      ),
    );
  }

  Widget _buildFaqItem(String question, String answer) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            question,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 4),
          Text(
            answer,
            style: TextStyle(
              fontSize: 13,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  // 헬퍼 메서드들
  String _getPlanDisplayName(SubscriptionPlanType planType) {
    switch (planType) {
      case SubscriptionPlanType.free:
        return '무료 플랜';
      case SubscriptionPlanType.plus:
        return '플러스 플랜';
      case SubscriptionPlanType.pro:
        return '프로 플랜';
    }
  }

  Color _getPlanColor(SubscriptionPlanType planType) {
    switch (planType) {
      case SubscriptionPlanType.free:
        return Colors.grey.shade700;
      case SubscriptionPlanType.plus:
        return Colors.blue.shade700;
      case SubscriptionPlanType.pro:
        return Colors.amber.shade700;
    }
  }



  /// 플랜별 아이콘
  IconData _getPlanIcon(SubscriptionPlanType planType) {
    switch (planType) {
      case SubscriptionPlanType.free:
        return Icons.star_border;
      case SubscriptionPlanType.plus:
        return Icons.star_half;
      case SubscriptionPlanType.pro:
        return Icons.star;
    }
  }

  Future<void> _upgradeToPlus() async {
    setState(() {
      _isUpgrading = true;
    });

    try {
      // 플러스 플랜 구독 관리 화면으로 이동하여 실제 결제 진행
      final result = await Navigator.of(context).push<bool>(
        MaterialPageRoute(
          builder: (context) => const SubscriptionManagementScreen(),
        ),
      );

      // 결제 성공 시 구독 상태 업데이트
      if (result == true) {
        await ref.read(subscriptionNotifierProvider.notifier).updatePlan(SubscriptionPlanType.plus);

        if (mounted) {
          ToastUtils.showSuccess(context, '플러스 플랜으로 업그레이드되었습니다!');
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      if (mounted) {
        ToastUtils.showError(context, '업그레이드에 실패했습니다: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpgrading = false;
        });
      }
    }
  }

  Future<void> _upgradeToPro() async {
    setState(() {
      _isUpgrading = true;
    });

    try {
      // 구독 관리 화면으로 이동하여 실제 결제 진행
      final result = await Navigator.of(context).push<bool>(
        MaterialPageRoute(
          builder: (context) => const SubscriptionManagementScreen(),
        ),
      );

      // 결제 성공 시 구독 상태 업데이트
      if (result == true) {
        await ref.read(subscriptionNotifierProvider.notifier).updatePlan(SubscriptionPlanType.pro);

        if (mounted) {
          ToastUtils.showSuccess(context, '프로 플랜으로 업그레이드되었습니다!');
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      if (mounted) {
        ToastUtils.showError(context, '업그레이드에 실패했습니다: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpgrading = false;
        });
      }
    }
  }
}
