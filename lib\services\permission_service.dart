/// 바라 부스 매니저 - 권한 관리 서비스
///
/// 행사 권한 관련 비즈니스 로직을 처리하는 서비스 클래스입니다.
/// - 사용자 권한 체크 및 검증
/// - 초대받은 사용자 식별
/// - 권한별 기능 제한 처리
/// - Firebase Firestore 연동
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/event_permission.dart';
import '../models/permission.dart';
import '../utils/logger_utils.dart';

/// 권한 관리 서비스 클래스
class PermissionService {
  static const String _tag = 'PermissionService';
  
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// 현재 사용자의 특정 행사에 대한 권한 정보 조회
  /// 
  /// [eventId] 행사 ID
  /// 반환값: 권한 정보 (권한이 없으면 null)
  Future<EventPermission?> getUserPermission(int eventId) async {
    LoggerUtils.methodStart('getUserPermission', tag: _tag, data: {
      'eventId': eventId,
    });

    final user = _auth.currentUser;
    if (user == null) {
      LoggerUtils.logWarning('로그인되지 않은 사용자', tag: _tag);
      return null;
    }

    try {
      // 먼저 소유자인지 확인 (users/{userId}/events/{eventId} 존재 여부)
      final ownerDoc = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('events')
          .doc(eventId.toString())
          .get();

      if (ownerDoc.exists) {
        // 소유자 권한 반환
        final permission = EventPermission.createOwner(
          eventId: eventId,
          userId: user.uid,
          userNickname: await _getUserNickname(user.uid),
        );
        LoggerUtils.logInfo('소유자 권한 확인: ${permission.userNickname}', tag: _tag);
        return permission;
      }

      // 초대받은 사용자 권한 확인
      final permissionDoc = await _firestore
          .collection('event_permissions')
          .doc(eventId.toString())
          .collection('users')
          .doc(user.uid)
          .get();

      if (permissionDoc.exists) {
        final permission = EventPermission.fromFirebaseMap(permissionDoc.data()!);
        LoggerUtils.logInfo('초대받은 사용자 권한 확인: ${permission.userNickname}', tag: _tag);
        return permission;
      }

      LoggerUtils.logInfo('권한 없음: eventId=$eventId, userId=${user.uid}', tag: _tag);
      return null;
    } catch (e) {
      LoggerUtils.logError('권한 정보 조회 실패', tag: _tag, error: e);
      return null;
    }
  }

  /// 사용자 닉네임 조회
  Future<String> _getUserNickname(String userId) async {
    try {
      final userDoc = await _firestore
          .collection('users')
          .doc(userId)
          .get();
      
      if (userDoc.exists && userDoc.data()?['nickname'] != null) {
        return userDoc.data()!['nickname'] as String;
      }
      return '알 수 없는 사용자';
    } catch (e) {
      LoggerUtils.logWarning('사용자 닉네임 조회 실패: $userId', tag: _tag, error: e);
      return '알 수 없는 사용자';
    }
  }

  /// 현재 사용자가 특정 행사의 소유자인지 확인
  /// 
  /// [eventId] 행사 ID
  /// 반환값: 소유자 여부
  Future<bool> isEventOwner(int eventId) async {
    final permission = await getUserPermission(eventId);
    return permission?.isOwner ?? false;
  }

  /// 현재 사용자가 특정 행사에 초대받은 사용자인지 확인
  /// 
  /// [eventId] 행사 ID
  /// 반환값: 초대받은 사용자 여부
  Future<bool> isInvitedUser(int eventId) async {
    final permission = await getUserPermission(eventId);
    return permission?.isInvited ?? false;
  }

  /// 특정 권한을 가지고 있는지 확인
  /// 
  /// [eventId] 행사 ID
  /// [permission] 확인할 권한
  /// 반환값: 권한 보유 여부
  Future<bool> hasPermission(int eventId, Permission permission) async {
    final userPermission = await getUserPermission(eventId);
    return userPermission?.hasPermission(permission) ?? false;
  }

  /// 읽기 권한 확인
  Future<bool> canRead(int eventId) async {
    return await hasPermission(eventId, Permission.read);
  }

  /// 다운로드 권한 확인
  Future<bool> canDownload(int eventId) async {
    return await hasPermission(eventId, Permission.download);
  }

  /// 선입금 관리 권한 확인
  Future<bool> canManagePrepayment(int eventId) async {
    return await hasPermission(eventId, Permission.prepaymentManage);
  }

  /// 생성/수정/삭제 권한 확인 (소유자만 가능)
  Future<bool> canModify(int eventId) async {
    return await isEventOwner(eventId);
  }

  /// 권한 없음 에러 메시지 반환
  String getPermissionDeniedMessage(Permission permission) {
    switch (permission) {
      case Permission.read:
        return '이 행사의 데이터를 볼 권한이 없습니다';
      case Permission.download:
        return '다운로드 권한이 없습니다';
      case Permission.prepaymentManage:
        return '선입금 관리 권한이 없습니다';
    }
  }

  /// 수정 권한 없음 에러 메시지
  String get modifyPermissionDeniedMessage => '초대받은 사용자는 데이터를 수정할 수 없습니다';

  /// 초대받은 사용자에게 권한 부여
  /// 
  /// [eventId] 행사 ID
  /// [userId] 사용자 ID
  /// [userNickname] 사용자 닉네임
  /// [grantedByUserId] 권한을 부여한 사용자 ID (소유자)
  /// [permissions] 부여할 권한 목록 (기본값: 기본 초대 권한)
  Future<EventPermission> grantPermission({
    required int eventId,
    required String userId,
    required String userNickname,
    required String grantedByUserId,
    List<Permission>? permissions,
  }) async {
    LoggerUtils.methodStart('grantPermission', tag: _tag, data: {
      'eventId': eventId,
      'userId': userId,
      'userNickname': userNickname,
      'grantedByUserId': grantedByUserId,
    });

    try {
      final permission = EventPermission.createInvited(
        eventId: eventId,
        userId: userId,
        userNickname: userNickname,
        grantedByUserId: grantedByUserId,
        customPermissions: permissions,
      );

      // Firestore에 저장
      await _firestore
          .collection('event_permissions')
          .doc(eventId.toString())
          .collection('users')
          .doc(userId)
          .set(permission.toFirebaseMap());

      LoggerUtils.logInfo('권한 부여 완료: $userNickname', tag: _tag);
      return permission;
    } catch (e) {
      LoggerUtils.logError('권한 부여 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 권한 제거 (추방)
  /// 
  /// [eventId] 행사 ID
  /// [userId] 제거할 사용자 ID
  Future<void> revokePermission(int eventId, String userId) async {
    LoggerUtils.methodStart('revokePermission', tag: _tag, data: {
      'eventId': eventId,
      'userId': userId,
    });

    try {
      await _firestore
          .collection('event_permissions')
          .doc(eventId.toString())
          .collection('users')
          .doc(userId)
          .delete();

      LoggerUtils.logInfo('권한 제거 완료: userId=$userId', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('권한 제거 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 행사의 모든 권한 사용자 목록 조회
  /// 
  /// [eventId] 행사 ID
  /// 반환값: 권한 사용자 목록
  Future<List<EventPermission>> getEventPermissions(int eventId) async {
    LoggerUtils.methodStart('getEventPermissions', tag: _tag, data: {
      'eventId': eventId,
    });

    try {
      final query = await _firestore
          .collection('event_permissions')
          .doc(eventId.toString())
          .collection('users')
          .get();

      final permissions = <EventPermission>[];
      for (final doc in query.docs) {
        try {
          final permission = EventPermission.fromFirebaseMap(doc.data());
          permissions.add(permission);
        } catch (e) {
          LoggerUtils.logWarning('권한 데이터 파싱 실패: ${doc.id}', tag: _tag, error: e);
        }
      }

      LoggerUtils.logInfo('권한 사용자 목록 조회 완료: ${permissions.length}명', tag: _tag);
      return permissions;
    } catch (e) {
      LoggerUtils.logError('권한 사용자 목록 조회 실패', tag: _tag, error: e);
      rethrow;
    }
  }

  /// 현재 사용자가 접근 가능한 행사 ID 목록 조회
  /// 
  /// 반환값: 접근 가능한 행사 ID 목록
  Future<List<int>> getAccessibleEventIds() async {
    LoggerUtils.methodStart('getAccessibleEventIds', tag: _tag);

    final user = _auth.currentUser;
    if (user == null) {
      return [];
    }

    try {
      final eventIds = <int>[];

      // 1. 소유한 행사들
      final ownedEvents = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('events')
          .get();

      for (final doc in ownedEvents.docs) {
        try {
          final eventId = int.parse(doc.id);
          eventIds.add(eventId);
        } catch (e) {
          LoggerUtils.logWarning('행사 ID 파싱 실패: ${doc.id}', tag: _tag);
        }
      }

      // 2. 초대받은 행사들
      final permissionQuery = await _firestore
          .collectionGroup('users')
          .where('userId', isEqualTo: user.uid)
          .get();

      for (final doc in permissionQuery.docs) {
        try {
          final permission = EventPermission.fromFirebaseMap(doc.data());
          if (!eventIds.contains(permission.eventId)) {
            eventIds.add(permission.eventId);
          }
        } catch (e) {
          LoggerUtils.logWarning('권한 데이터 파싱 실패: ${doc.id}', tag: _tag);
        }
      }

      LoggerUtils.logInfo('접근 가능한 행사 ${eventIds.length}개 조회 완료', tag: _tag);
      return eventIds;
    } catch (e) {
      LoggerUtils.logError('접근 가능한 행사 목록 조회 실패', tag: _tag, error: e);
      return [];
    }
  }
}
