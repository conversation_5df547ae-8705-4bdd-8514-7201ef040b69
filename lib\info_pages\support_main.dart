import 'package:flutter/material.dart';
import 'common_theme.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(const SupportApp());
}

class SupportApp extends StatelessWidget {
  const SupportApp({super.key});
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: '고객 지원 - Parabara',
      theme: InfoTheme.theme(),
      home: const SafeArea(child: SupportPage()),
    );
  }
}

class SupportPage extends StatelessWidget {
  const SupportPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          children: [
            // 헤더 섹션
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 60),
              decoration: const BoxDecoration(
                gradient: InfoTheme.heroGradient,
              ),
              child: Center(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 800),
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: InfoTheme.secondary.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: const Icon(Icons.support_agent, color: InfoTheme.secondary, size: 40),
                      ),
                      const SizedBox(height: 24),
                      Text(
                        '고객 지원',
                        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.w800,
                          color: InfoTheme.textPrimary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        '바라 부스 매니저 사용 중 문의사항이나 도움이 필요하시면\n언제든지 연락해 주세요.',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: InfoTheme.textSecondary,
                          height: 1.6,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // 콘텐츠 섹션
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 60),
              child: Center(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 900),
                  child: Column(
                    children: [
                      // 연락처 카드
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(32),
                        decoration: BoxDecoration(
                          gradient: InfoTheme.primaryGradient,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: InfoTheme.cardShadow,
                        ),
                        child: Column(
                          children: [
                            const Icon(Icons.email, color: Colors.white, size: 32),
                            const SizedBox(height: 16),
                            const Text(
                              '문의하기',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.w700,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(height: 8),
                            const SelectableText(
                              '<EMAIL>',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Column(
                                children: [
                                  Text(
                                    '📞 응답 시간',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      color: Colors.white,
                                    ),
                                  ),
                                  SizedBox(height: 8),
                                  Text(
                                    '• 평일: 24시간 이내 응답\n• 주말/공휴일: 48시간 이내 응답\n• 긴급한 문제: 가능한 한 빠른 시간 내 응답',
                                    style: TextStyle(
                                      color: Colors.white70,
                                      height: 1.5,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 48),

                      // FAQ 섹션
                      Text(
                        '자주 묻는 질문 (FAQ)',
                        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.w700,
                          color: InfoTheme.textPrimary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 32),

                      _buildFaqItem(
                        'Q. 앱이 실행되지 않아요.',
                        'A. 앱을 완전히 종료한 후 다시 실행해 보세요. 문제가 지속되면 앱을 재설치하거나 기기를 재시작해 보세요.',
                        Icons.smartphone,
                      ),
                      const SizedBox(height: 16),
                      _buildFaqItem(
                        'Q. 데이터가 동기화되지 않아요.',
                        'A. 인터넷 연결을 확인하고, 앱 설정에서 동기화 기능이 활성화되어 있는지 확인해 주세요.',
                        Icons.sync,
                      ),
                      const SizedBox(height: 16),
                      _buildFaqItem(
                        'Q. 계정을 삭제하고 싶어요.',
                        'A. 앱 내 설정 → 계정 관리에서 삭제하거나, 계정 삭제 페이지를 참고해 주세요.',
                        Icons.delete_outline,
                      ),
                      const SizedBox(height: 16),
                      _buildFaqItem(
                        'Q. 구독을 취소하고 싶어요.',
                        'A. 앱 내 설정 → 구독 관리에서 취소하거나, 고객 지원으로 연락해 주세요.',
                        Icons.cancel,
                      ),
                    ],
                  ),
                ),
              ),
            ),

            const CompanyFooter(),
          ],
        ),
      ),
    );
  }

  Widget _buildFaqItem(String question, String answer, IconData icon) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: InfoTheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: InfoTheme.cardShadow,
        border: Border.all(color: InfoTheme.borderLight),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: InfoTheme.secondary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: InfoTheme.secondary, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  question,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w700,
                    color: InfoTheme.textPrimary,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            answer,
            style: const TextStyle(
              color: InfoTheme.textSecondary,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }


}

