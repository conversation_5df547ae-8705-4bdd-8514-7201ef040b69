import 'package:flutter/material.dart';
import 'common_theme.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(const AccountDeletionApp());
}

class AccountDeletionApp extends StatelessWidget {
  const AccountDeletionApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: '계정 삭제 요청 - Parabara',
      theme: InfoTheme.theme(),
      home: const SafeArea(child: AccountDeletionPage()),
    );
  }
}

class AccountDeletionPage extends StatelessWidget {
  const AccountDeletionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          children: [
            // 헤더 섹션
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 60),
              decoration: const BoxDecoration(
                gradient: InfoTheme.heroGradient,
              ),
              child: Center(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 800),
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: InfoTheme.primary.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: const Icon(Icons.delete_outline, color: InfoTheme.primary, size: 40),
                      ),
                      const SizedBox(height: 24),
                      Text(
                        '계정 삭제 요청',
                        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.w800,
                          color: InfoTheme.textPrimary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        '바라 부스 매니저 앱에서 회원 탈퇴 및 개인정보 삭제를 원하시는 경우,\n아래 절차를 따라 요청해 주세요.',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: InfoTheme.textSecondary,
                          height: 1.6,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // 콘텐츠 섹션
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 60),
              child: Center(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 800),
                  child: Column(
                    children: [
                      // 삭제 방법
                      _buildMethodCard(
                        '1',
                        '앱 내에서 계정 삭제',
                        '바라 부스 매니저 앱 → 설정 → 계정 관리 → 계정 삭제 메뉴를 이용해 주세요.',
                        Icons.smartphone,
                      ),
                      const SizedBox(height: 24),
                      _buildMethodCard(
                        '2',
                        '이메일로 삭제 요청',
                        '앱에서 삭제가 어려운 경우, 아래 이메일로 직접 연락 주세요.',
                        Icons.email,
                      ),
                      const SizedBox(height: 32),

                      // 연락처 카드
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(32),
                        decoration: BoxDecoration(
                          gradient: InfoTheme.primaryGradient,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: InfoTheme.cardShadow,
                        ),
                        child: Column(
                          children: [
                            const Icon(Icons.email, color: Colors.white, size: 32),
                            const SizedBox(height: 16),
                            const Text(
                              '연락처',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.w700,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(height: 8),
                            const SelectableText(
                              '<EMAIL>',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '계정 삭제 요청 시 다음 정보를 포함해 주세요:',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      color: Colors.white,
                                    ),
                                  ),
                                  SizedBox(height: 8),
                                  Text(
                                    '• 등록된 이메일 주소\n• 삭제 요청 사유 (선택사항)\n• 본인 확인을 위한 추가 정보',
                                    style: TextStyle(
                                      color: Colors.white70,
                                      height: 1.5,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            const CompanyFooter(),
          ],
        ),
      ),
    );
  }

  Widget _buildMethodCard(String number, String title, String description, IconData icon) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: InfoTheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: InfoTheme.cardShadow,
        border: Border.all(color: InfoTheme.borderLight),
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              gradient: InfoTheme.primaryGradient,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Text(
                number,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w800,
                  color: Colors.white,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(icon, color: InfoTheme.primary, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w700,
                        color: InfoTheme.textPrimary,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  description,
                  style: const TextStyle(
                    color: InfoTheme.textSecondary,
                    height: 1.5,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }


}

