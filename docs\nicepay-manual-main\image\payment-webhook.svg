<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than diagrams.net -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="522px" height="135px" viewBox="-0.5 -0.5 522 135" content="&lt;mxfile host=&quot;Electron&quot; modified=&quot;2022-09-27T05:28:50.070Z&quot; agent=&quot;5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/20.3.0 Chrome/104.0.5112.114 Electron/20.1.3 Safari/537.36&quot; etag=&quot;ZlmHf8Y41uE9yN4rDceu&quot; version=&quot;20.3.0&quot; type=&quot;device&quot;&gt;&lt;diagram id=&quot;sRzzeWXZqnTvxZI4A3nx&quot; name=&quot;페이지-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;"><defs/><g><rect x="38.5" y="69.5" width="104" height="24.57" rx="12.29" ry="12.29" fill="#f6f8fa" stroke="none" transform="rotate(90,90.5,81.78)" pointer-events="all"/><rect x="335" y="69.5" width="104" height="24.57" rx="12.29" ry="12.29" fill="#f6f8fa" stroke="none" transform="rotate(90,387,81.78)" pointer-events="all"/><path d="M 85.5 54 Q 65.52 54 65.52 79.56 Q 65.52 105.12 85.5 105.07" fill="none" stroke="#c9c9c9" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 85.5 48 L 95.5 54 L 85.5 60 Z" fill="#c1411a" stroke="none" transform="rotate(-180,90.5,54)" pointer-events="all"/><path d="M 95.5 105.07 L 232 105.07" fill="none" stroke="#c9c9c9" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><path d="M 85.5 99.07 L 95.5 105.07 L 85.5 111.07 Z" fill="#c1411a" stroke="none" pointer-events="all"/><path d="M 381.5 54 Q 381.5 54 95.5 54" fill="none" stroke="#c9c9c9" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 381.5 48 L 391.5 54 L 381.5 60 Z" fill="#233976" stroke="none" transform="rotate(-180,386.5,54)" pointer-events="all"/><path d="M 391.5 83.07 Q 409 83.12 409 68.56 Q 409 54 391.5 54" fill="none" stroke="#c9c9c9" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><ellipse cx="387" cy="83.07" rx="4.5" ry="4.5" fill="#233976" stroke="none" pointer-events="all"/><rect x="154.5" y="36.07" width="170" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 168px; height: 1px; padding-top: 51px; margin-left: 156px;"><div data-drawio-colors="color: #8C8C8C; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: &quot;맑은 고딕&quot;; color: rgb(140, 140, 140); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 8px;">POST &lt;웹훅 End-Point&gt; <br />승인 응답 전달</font></div></div></div></foreignObject><text x="240" y="55" fill="#8C8C8C" font-family="맑은 고딕" font-size="12px" text-anchor="middle">POST &lt;웹훅 End-Point&gt;...</text></switch></g><rect x="119" y="103" width="100" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 118px; margin-left: 120px;"><div data-drawio-colors="color: #8C8C8C; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: &quot;맑은 고딕&quot;; color: rgb(140, 140, 140); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div style="text-align: left;"><span style="font-size: 8px; background-color: initial;">HTTP status 200</span></div><span style="font-size: 8px;"><div style="text-align: left;"><span style="background-color: initial;">ResponseBody { "OK" }</span></div></span></div></div></div></foreignObject><text x="169" y="122" fill="#8C8C8C" font-family="맑은 고딕" font-size="12px" text-anchor="middle">HTTP status 200R...</text></switch></g><ellipse cx="239.5" cy="105.07" rx="7.5" ry="7.5" fill="rgb(255, 255, 255)" stroke="#c1411a" stroke-width="2" pointer-events="none"/><path d="M 235.5 105.07 L 243.5 105.07" fill="none" stroke="#c1411a" stroke-width="2" stroke-miterlimit="10" transform="rotate(135,239.5,105.07)" pointer-events="none"/><path d="M 235.5 105.07 L 243.5 105.07" fill="none" stroke="#c1411a" stroke-width="2" stroke-miterlimit="10" transform="rotate(45,239.5,105.07)" pointer-events="none"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 48px; height: 1px; padding-top: 74px; margin-left: 12px;"><div data-drawio-colors="color: #8C8C8C; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: &quot;맑은 고딕&quot;; color: rgb(140, 140, 140); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><div style="text-align: left;"><span style="font-size: 8px;">비즈니스 </span></div></div></div></div></foreignObject><text x="36" y="78" fill="#8C8C8C" font-family="맑은 고딕" font-size="12px" text-anchor="middle">비즈니스 </text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 92px; margin-left: 393px;"><div data-drawio-colors="color: #8C8C8C; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: &quot;맑은 고딕&quot;; color: rgb(140, 140, 140); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><div style="text-align: left;"><span style="font-size: 8px;">ResponseBody에 </span></div></div></div></div></foreignObject><text x="442" y="96" fill="#8C8C8C" font-family="맑은 고딕" font-size="12px" text-anchor="middle">ResponseBody에 </text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 15px; margin-left: 72px;"><div data-drawio-colors="color: #8C8C8C; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 13px; font-family: &quot;맑은 고딕&quot;; color: rgb(140, 140, 140); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><span style="font-size: 13px;">Server</span></div></div></div></foreignObject><text x="91" y="19" fill="#8C8C8C" font-family="맑은 고딕" font-size="13px" text-anchor="middle">Server</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 15px; margin-left: 368px;"><div data-drawio-colors="color: #8C8C8C; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 13px; font-family: &quot;맑은 고딕&quot;; color: rgb(140, 140, 140); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><span style="font-size: 13px;">Nicepay</span></div></div></div></foreignObject><text x="387" y="19" fill="#8C8C8C" font-family="맑은 고딕" font-size="13px" text-anchor="middle">Nicepay</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 128px; height: 1px; padding-top: 102px; margin-left: 392px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 13px; font-family: &quot;맑은 고딕&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><div style="color: rgb(140, 140, 140); font-family: &quot;맑은 고딕&quot;; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;"><span style="font-size: 8px;">"OK" 문자열이 없는 경우</span></div></div></div></div></foreignObject><text x="456" y="106" fill="rgb(0, 0, 0)" font-family="맑은 고딕" font-size="13px" text-anchor="middle">"OK" 문자열이 없는 경우</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 128px; height: 1px; padding-top: 113px; margin-left: 393px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 13px; font-family: &quot;맑은 고딕&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><span style="color: rgb(140, 140, 140); font-family: &quot;맑은 고딕&quot;; font-size: 8px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;">1분 간격으로 10회 재전송</span></div></div></div></foreignObject><text x="457" y="117" fill="rgb(0, 0, 0)" font-family="맑은 고딕" font-size="13px" text-anchor="middle">1분 간격으로 10회 재전송</text></switch></g><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 68px; height: 1px; padding-top: 85px; margin-left: 2px;"><div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 13px; font-family: &quot;맑은 고딕&quot;; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: none; white-space: normal; overflow-wrap: normal;"><span style="color: rgb(140, 140, 140); font-family: &quot;맑은 고딕&quot;; font-size: 8px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;">로직 처리</span></div></div></div></foreignObject><text x="36" y="88" fill="rgb(0, 0, 0)" font-family="맑은 고딕" font-size="13px" text-anchor="middle">로직 처리</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>