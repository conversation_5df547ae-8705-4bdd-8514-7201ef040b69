/// 바라 부스 매니저 - 권한 Provider
///
/// 행사 권한 관련 상태 관리 및 비즈니스 로직을 처리하는 Provider입니다.
/// - 사용자 권한 체크 및 관리
/// - 권한 부여 및 제거
/// - 접근 제어
/// - Riverpod 기반 상태 관리
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/event_permission.dart';
import '../models/permission.dart';
import '../services/permission_service.dart';
import '../repositories/event_permission_repository.dart';
import '../utils/logger_utils.dart';

/// 권한 상태 클래스
class PermissionState {
  final Map<int, EventPermission> eventPermissions; // eventId -> permission
  final bool isLoading;
  final String? errorMessage;

  const PermissionState({
    this.eventPermissions = const {},
    this.isLoading = false,
    this.errorMessage,
  });

  PermissionState copyWith({
    Map<int, EventPermission>? eventPermissions,
    bool? isLoading,
    String? errorMessage,
  }) {
    return PermissionState(
      eventPermissions: eventPermissions ?? this.eventPermissions,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
    );
  }

  /// 특정 행사의 권한 조회
  EventPermission? getPermission(int eventId) {
    return eventPermissions[eventId];
  }

  /// 특정 행사의 소유자 여부 확인
  bool isOwner(int eventId) {
    return getPermission(eventId)?.isOwner ?? false;
  }

  /// 특정 행사의 초대받은 사용자 여부 확인
  bool isInvited(int eventId) {
    return getPermission(eventId)?.isInvited ?? false;
  }

  /// 특정 권한 보유 여부 확인
  bool hasPermission(int eventId, Permission permission) {
    return getPermission(eventId)?.hasPermission(permission) ?? false;
  }
}

/// 권한 Provider
class PermissionNotifier extends StateNotifier<PermissionState> {
  static const String _tag = 'PermissionNotifier';
  
  final PermissionService _permissionService;
  final EventPermissionRepository _repository;

  PermissionNotifier(this._permissionService, this._repository) : super(const PermissionState());

  /// 특정 행사의 권한 로드
  ///
  /// [eventId] 행사 ID
  Future<void> loadPermission(int eventId) async {
    LoggerUtils.methodStart('loadPermission', tag: _tag, data: {
      'eventId': eventId,
    });

    try {
      // Service와 Repository를 함께 사용하여 권한 정보 로드
      final permission = await _permissionService.getUserPermission(eventId);

      if (permission != null) {
        // Repository를 통해 추가 권한 정보 확인 및 캐싱
        await _repository.getPermission(eventId, permission.userId);

        final updatedPermissions = Map<int, EventPermission>.from(state.eventPermissions);
        updatedPermissions[eventId] = permission;

        state = state.copyWith(eventPermissions: updatedPermissions);
        LoggerUtils.logInfo('권한 로드 완료: ${permission.role.displayName}', tag: _tag);
      } else {
        LoggerUtils.logInfo('권한 없음: eventId=$eventId', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('권한 로드 실패', tag: _tag, error: e);
      state = state.copyWith(errorMessage: e.toString());
    }
  }

  /// 권한 부여 (초대받은 사용자 등록)
  /// 
  /// [eventId] 행사 ID
  /// [userId] 사용자 ID
  /// [userNickname] 사용자 닉네임
  /// [permissions] 부여할 권한 목록 (기본값: 기본 초대 권한)
  Future<EventPermission> grantPermission({
    required int eventId,
    required String userId,
    required String userNickname,
    List<Permission>? permissions,
  }) async {
    LoggerUtils.methodStart('grantPermission', tag: _tag, data: {
      'eventId': eventId,
      'userId': userId,
      'userNickname': userNickname,
    });

    state = state.copyWith(isLoading: true, errorMessage: null);

    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        throw Exception('로그인이 필요합니다');
      }

      final permission = await _permissionService.grantPermission(
        eventId: eventId,
        userId: userId,
        userNickname: userNickname,
        grantedByUserId: currentUser.uid,
        permissions: permissions,
      );

      state = state.copyWith(isLoading: false);
      LoggerUtils.logInfo('권한 부여 완료: $userNickname', tag: _tag);
      return permission;
    } catch (e) {
      LoggerUtils.logError('권한 부여 실패', tag: _tag, error: e);
      state = state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      );
      rethrow;
    }
  }

  /// 권한 제거 (추방)
  /// 
  /// [eventId] 행사 ID
  /// [userId] 제거할 사용자 ID
  Future<void> revokePermission(int eventId, String userId) async {
    LoggerUtils.methodStart('revokePermission', tag: _tag, data: {
      'eventId': eventId,
      'userId': userId,
    });

    state = state.copyWith(isLoading: true, errorMessage: null);

    try {
      await _permissionService.revokePermission(eventId, userId);
      state = state.copyWith(isLoading: false);
      LoggerUtils.logInfo('권한 제거 완료: userId=$userId', tag: _tag);
    } catch (e) {
      LoggerUtils.logError('권한 제거 실패', tag: _tag, error: e);
      state = state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      );
      rethrow;
    }
  }

  /// 현재 사용자가 접근 가능한 행사 ID 목록 조회
  Future<List<int>> getAccessibleEventIds() async {
    LoggerUtils.methodStart('getAccessibleEventIds', tag: _tag);

    try {
      final eventIds = await _permissionService.getAccessibleEventIds();
      LoggerUtils.logInfo('접근 가능한 행사 ${eventIds.length}개 조회 완료', tag: _tag);
      return eventIds;
    } catch (e) {
      LoggerUtils.logError('접근 가능한 행사 목록 조회 실패', tag: _tag, error: e);
      return [];
    }
  }

  /// 특정 행사의 소유자 여부 확인
  Future<bool> isEventOwner(int eventId) async {
    return await _permissionService.isEventOwner(eventId);
  }

  /// 특정 행사의 초대받은 사용자 여부 확인
  Future<bool> isInvitedUser(int eventId) async {
    return await _permissionService.isInvitedUser(eventId);
  }

  /// 특정 권한 보유 여부 확인
  Future<bool> hasPermission(int eventId, Permission permission) async {
    return await _permissionService.hasPermission(eventId, permission);
  }

  /// 읽기 권한 확인
  Future<bool> canRead(int eventId) async {
    return await _permissionService.canRead(eventId);
  }

  /// 다운로드 권한 확인
  Future<bool> canDownload(int eventId) async {
    return await _permissionService.canDownload(eventId);
  }

  /// 선입금 관리 권한 확인
  Future<bool> canManagePrepayment(int eventId) async {
    return await _permissionService.canManagePrepayment(eventId);
  }

  /// 생성/수정/삭제 권한 확인 (소유자만 가능)
  Future<bool> canModify(int eventId) async {
    return await _permissionService.canModify(eventId);
  }

  /// 권한 없음 에러 메시지 반환
  String getPermissionDeniedMessage(Permission permission) {
    return _permissionService.getPermissionDeniedMessage(permission);
  }

  /// 수정 권한 없음 에러 메시지
  String get modifyPermissionDeniedMessage => _permissionService.modifyPermissionDeniedMessage;

  /// 에러 메시지 클리어
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }
}

/// 권한 서비스 Provider
final permissionServiceProvider = Provider<PermissionService>((ref) {
  return PermissionService();
});

/// 권한 Repository Provider
final eventPermissionRepositoryProvider = Provider<EventPermissionRepository>((ref) {
  return EventPermissionRepository();
});

/// 권한 Provider
final permissionNotifierProvider = StateNotifierProvider<PermissionNotifier, PermissionState>((ref) {
  final permissionService = ref.read(permissionServiceProvider);
  final repository = ref.read(eventPermissionRepositoryProvider);
  return PermissionNotifier(permissionService, repository);
});

/// 특정 행사의 권한 Provider
final eventPermissionProvider = FutureProvider.family<EventPermission?, int>((ref, eventId) async {
  final permissionService = ref.read(permissionServiceProvider);
  return await permissionService.getUserPermission(eventId);
});

/// 특정 행사의 모든 권한 사용자 목록 Provider
final eventPermissionsProvider = FutureProvider.family<List<EventPermission>, int>((ref, eventId) async {
  final repository = ref.read(eventPermissionRepositoryProvider);
  return await repository.getEventPermissions(eventId);
});

/// 특정 행사의 초대받은 사용자 목록 Provider
final invitedUsersProvider = FutureProvider.family<List<EventPermission>, int>((ref, eventId) async {
  final repository = ref.read(eventPermissionRepositoryProvider);
  return await repository.getInvitedUsers(eventId);
});

/// 접근 가능한 행사 ID 목록 Provider
final accessibleEventIdsProvider = FutureProvider<List<int>>((ref) async {
  final permissionService = ref.read(permissionServiceProvider);
  return await permissionService.getAccessibleEventIds();
});
