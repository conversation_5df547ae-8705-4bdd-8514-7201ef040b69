<?xml version="1.0" encoding="UTF-8"?>
<!-- Do not edit this file with editors other than diagrams.net -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="522px" height="137px" viewBox="-0.5 -0.5 522 137" content="&lt;mxfile host=&quot;Electron&quot; modified=&quot;2022-09-27T05:35:20.226Z&quot; agent=&quot;5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/20.3.0 Chrome/104.0.5112.114 Electron/20.1.3 Safari/537.36&quot; etag=&quot;XuXIHlMiEWMF8pQc8L6o&quot; version=&quot;20.3.0&quot; type=&quot;device&quot;&gt;&lt;diagram id=&quot;sRzzeWXZqnTvxZI4A3nx&quot; name=&quot;페이지-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;"><defs/><g><rect x="36.5" y="71.5" width="104" height="24.57" rx="12.29" ry="12.29" fill="#f6f8fa" stroke="none" transform="rotate(90,88.5,83.78)" pointer-events="all"/><rect x="333" y="71.5" width="104" height="24.57" rx="12.29" ry="12.29" fill="#f6f8fa" stroke="none" transform="rotate(90,385,83.78)" pointer-events="all"/><path d="M 83.5 56 Q 63.52 56 63.52 81.56 Q 63.52 107.12 83.5 107.07" fill="none" stroke="#c9c9c9" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 83.5 50 L 93.5 56 L 83.5 62 Z" fill="#c1411a" stroke="none" transform="rotate(-180,88.5,56)" pointer-events="all"/><path d="M 93.5 107.07 L 380.5 107.07" fill="none" stroke="#c9c9c9" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 83.5 101.07 L 93.5 107.07 L 83.5 113.07 Z" fill="#c1411a" stroke="none" pointer-events="all"/><path d="M 379.5 56 Q 379.5 56 93.5 56" fill="none" stroke="#c9c9c9" stroke-miterlimit="10" pointer-events="stroke"/><path d="M 379.5 50 L 389.5 56 L 379.5 62 Z" fill="#233976" stroke="none" transform="rotate(-180,384.5,56)" pointer-events="all"/><path d="M 389.5 85.07 Q 407 85.12 407 70.56 Q 407 56 389.5 56" fill="none" stroke="#c9c9c9" stroke-miterlimit="10" stroke-dasharray="3 3" pointer-events="stroke"/><ellipse cx="385" cy="85.07" rx="4.5" ry="4.5" fill="#233976" stroke="none" pointer-events="all"/><rect x="147" y="38" width="170" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 168px; height: 1px; padding-top: 53px; margin-left: 148px;"><div data-drawio-colors="color: #8C8C8C; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: &quot;맑은 고딕&quot;; color: rgb(140, 140, 140); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><font style="font-size: 8px;">POST &lt;웹훅 End-Point&gt; <br />Test 메시지 전달</font></div></div></div></foreignObject><text x="232" y="57" fill="#8C8C8C" font-family="맑은 고딕" font-size="12px" text-anchor="middle">POST &lt;웹훅 End-Point&gt;...</text></switch></g><rect x="182" y="88.57" width="100" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 104px; margin-left: 183px;"><div data-drawio-colors="color: #8C8C8C; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: &quot;맑은 고딕&quot;; color: rgb(140, 140, 140); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div style="text-align: left;"><span style="font-size: 8px; background-color: initial;">HTTP status 200</span></div><span style="font-size: 8px;"><div style="text-align: left;"><span style="background-color: initial;">ResponseBody { "OK" }</span></div></span></div></div></div></foreignObject><text x="232" y="107" fill="#8C8C8C" font-family="맑은 고딕" font-size="12px" text-anchor="middle">HTTP status 200R...</text></switch></g><rect x="7" y="62" width="50" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 48px; height: 1px; padding-top: 77px; margin-left: 8px;"><div data-drawio-colors="color: #8C8C8C; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: &quot;맑은 고딕&quot;; color: rgb(140, 140, 140); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div style="text-align: left;"><span style="font-size: 8px;">비즈니스 </span></div></div></div></div></foreignObject><text x="32" y="81" fill="#8C8C8C" font-family="맑은 고딕" font-size="12px" text-anchor="middle">비즈니스 </text></switch></g><rect x="375.5" y="82.32" width="112" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 110px; height: 1px; padding-top: 97px; margin-left: 377px;"><div data-drawio-colors="color: #8C8C8C; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 12px; font-family: &quot;맑은 고딕&quot;; color: rgb(140, 140, 140); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><div style="text-align: left;"><span style="font-size: 8px;">ResponseBody에 </span></div></div></div></div></foreignObject><text x="432" y="101" fill="#8C8C8C" font-family="맑은 고딕" font-size="12px" text-anchor="middle">ResponseBody에 </text></switch></g><rect x="68.5" y="0" width="40" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 15px; margin-left: 70px;"><div data-drawio-colors="color: #8C8C8C; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 13px; font-family: &quot;맑은 고딕&quot;; color: rgb(140, 140, 140); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 13px;">Server</span></div></div></div></foreignObject><text x="89" y="19" fill="#8C8C8C" font-family="맑은 고딕" font-size="13px" text-anchor="middle">Server</text></switch></g><rect x="365" y="0" width="40" height="30" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 38px; height: 1px; padding-top: 15px; margin-left: 366px;"><div data-drawio-colors="color: #8C8C8C; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 13px; font-family: &quot;맑은 고딕&quot;; color: rgb(140, 140, 140); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;"><span style="font-size: 13px;">Nicepay</span></div></div></div></foreignObject><text x="385" y="19" fill="#8C8C8C" font-family="맑은 고딕" font-size="13px" text-anchor="middle">Nicepay</text></switch></g><path d="M 380.5 101.07 L 390.5 107.07 L 380.5 113.07 Z" fill="#233976" stroke="none" pointer-events="all"/><rect x="390.5" y="98.57" width="130" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 109px; margin-left: 456px;"><div data-drawio-colors="color: #8C8C8C; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 8px; font-family: &quot;맑은 고딕&quot;; color: rgb(140, 140, 140); line-height: 1.2; pointer-events: all; white-space: nowrap;"><font style="font-size: 8px;">"OK" 문자열이 없는 경우 실패</font></div></div></div></foreignObject><text x="456" y="111" fill="#8C8C8C" font-family="맑은 고딕" font-size="8px" text-anchor="middle">"OK" 문자열이 없는 경우 실패</text></switch></g><rect x="0" y="77.57" width="60" height="20" fill="none" stroke="none" pointer-events="all"/><g transform="translate(-0.5 -0.5)"><switch><foreignObject pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 88px; margin-left: 30px;"><div data-drawio-colors="color: #8C8C8C; " style="box-sizing: border-box; font-size: 0px; text-align: center;"><div style="display: inline-block; font-size: 8px; font-family: &quot;맑은 고딕&quot;; color: rgb(140, 140, 140); line-height: 1.2; pointer-events: all; white-space: nowrap;">로직 처리</div></div></div></foreignObject><text x="30" y="90" fill="#8C8C8C" font-family="맑은 고딕" font-size="8px" text-anchor="middle">로직 처리</text></switch></g></g><switch><g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/><a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank"><text text-anchor="middle" font-size="10px" x="50%" y="100%">Text is not SVG - cannot display</text></a></switch></svg>