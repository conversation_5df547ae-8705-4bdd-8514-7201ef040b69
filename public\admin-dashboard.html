<!DOCTYPE html>
<html>
<head>
  <base href="$FLUTTER_BASE_HREF">
  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Parabara Admin Dashboard">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="Parabara Admin">
  <link rel="apple-touch-icon" href="admin-dashboard/icons/Icon-192.png">
  <link rel="icon" type="image/png" href="admin-dashboard/favicon.png"/>
  <title>Parabara Admin Dashboard</title>
  <link rel="manifest" href="admin-dashboard/manifest.json">
  <style>
    body {
      margin: 0;
      padding: 0;
      background-color: #f8f9fa;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>o, sans-serif;
    }
    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      flex-direction: column;
    }
    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #e9ecef;
      border-top: 4px solid #495057;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    .loading-text {
      margin-top: 16px;
      color: #6c757d;
      font-size: 14px;
    }
    .admin-header {
      background: white;
      padding: 16px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      text-align: center;
      margin-bottom: 20px;
    }
    .admin-header h1 {
      margin: 0;
      color: #495057;
      font-size: 24px;
      font-weight: 600;
    }
  </style>
</head>
<body>
  <div class="admin-header">
    <h1>🔧 Parabara Admin Dashboard</h1>
  </div>
  
  <div class="loading" id="loading">
    <div class="spinner"></div>
    <div class="loading-text">관리자 페이지 로딩 중...</div>
  </div>
  
  <script>
    // Flutter 앱 로드 설정
    window.addEventListener('load', function(ev) {
      // Flutter 앱 로드
      _flutter.loader.loadEntrypoint({
        serviceWorker: {
          serviceWorkerVersion: serviceWorkerVersion,
        },
        entrypointUrl: "admin-dashboard/main.dart.js",
        onEntrypointLoaded: function(engineInitializer) {
          engineInitializer.initializeEngine().then(function(appRunner) {
            appRunner.runApp();
            document.getElementById('loading').style.display = 'none';
          });
        }
      });
    });
  </script>
  <script src="admin-dashboard/flutter_bootstrap.js" async></script>
</body>
</html>
