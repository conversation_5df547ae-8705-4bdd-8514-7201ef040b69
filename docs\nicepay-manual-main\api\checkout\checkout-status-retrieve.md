# 체크아웃

## 체크아웃 상태 조회

체크아웃 서비스는 1transaction 서비스를 보다 쉽게 사용할 수 있도록 제공하기 위해 만들어진 서비스 입니다.
체크아웃 서비스는 결제창 호출 및 승인 관련 API들이 존재합니다.

| 설명          | HttpMethod | URI                                                |
|:------------|:----------:|:---------------------------------------------------|
| 체크아웃 발급     |    POST    | /v1/checkout                                       |
| 체크아웃 승인     |    POST    | /v1/checkout/pay/{encodeMerchantToken}/{sessionId} |
| 체크아웃 만료     |    POST    | /v1/checkout/{sessionId}/expire                    |
| **체크아웃 조회** |    GET     | **/v1/checkout/{sessionId}**                       |
| 체크아웃 거래 조회  |    GET     | /v1/payments/checkout/{sessionId}                  |
| 체크아웃 거래 취소  |    POST    | /v1/payments/checkout/{sessionId}/cancel           |

### 설명

체크아웃 발급 및 이벤트를 확인하기 위한 체크아웃 조회 기능입니다.
주요 확인 필드로는 체크아웃의 발급, 거래, 만료 상태 등을 확인할 수 있습니다.  

#### ⚠️ 중요

해당 서비스는 1transaction 서비스로 key 발급 시 `client 승인 & basic 인증`으로 발급 받으셔야 합니다.

### 요청 명세

```bash
curl -X GET 'https://api.nicepay.co.kr/v1/checkout/{sessionId}'
-H 'Content-type: application/json'
-H 'Authorization: Basic ZWVjOGQzNTA4Y2IwNDI1ZGI5NTViMzBiZjM5...'
```

#### 공통

|      구분      |    필드     |   타입   | 필수 | 길이  | 설명     | 상세설명                              |
|:------------:|:---------:|:------:|:--:|:---:|:-------|:----------------------------------|
| PathVariable | sessionId | String | O  | 64	 | 결제정보 키 | 가맹점이 요청한 sessionId                | 


<br>

### 응답 명세

|        필드         |   타입    |  필수   |  길이  | 설명                                | 상세설명                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
|:-----------------:|:-------:|:-----:|:----:|:----------------------------------|:-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|    resultCode     | String  |   O   |  4   | 처리결과코드                            | 0000 : 성공 / 그외 실패                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
|     resultMsg     | String  |   O   | 100  | 처리결과메시지                           | "정상 처리되었습니다."                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
|     sessionId     | String  |   O   | 64	  | 체크아웃 발급 키                         | 가맹점 결제정보 식별 키                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
|      orderId      | String  |   O   |  64  | 상점 거래 고유번호                        |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
|     clientId      | String  |   O   |  50  | 가맹점 식별코드                          | NICEPAY가 발급한 가맹점 식별값                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
|        tid        | String  |       |  30  | 결제 승인 키                           | **[승인 결과]** <br/> 인증성공후 승인이 시도된 경우 리턴됩니다. <br>승인(가상계좌-채번)에 사용된 NICEPAY 거래키 입니다.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
|      amount       | Integer |   O   |  12  | 결제 금액                             |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
|     goodsName     | String  |   O   |  40  | 상품명                               | 상품이름 <br/> (", * 특수문자 이용불가)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
|    receiptUrl     | String  |       | 200  | 매출전표 확인 URL                       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
|   mallReserved    | String  |       | 500  | 상점 예약필드                           | 상점 정보 전달용 예비필드<br>returnUrl로 redirect되는 시점에 반환 됩니다.<br>JSON string format으로 이용하시기를 권고 드립니다.<br>단, 큰따옴표(")는 이용불가                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
|      status       | String  |       |  20  | 결제 처리상태                           | **[상태 정보]** <br/> - paid:결제완료 <br/> - ready:준비됨(가상계좌 채번) <br/> - failed:결제실패 <br/> - cancelled:취소됨<br/> - partialCancelled:부분 취소됨<br/> - expired:만료됨                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
|     skinType      | String  |       | 10	  | 결제창 스킨 설정<br>                     | 지원 옵션 <br/> red/green/purple/gray/dark                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
|    taxFreeAmt     | Integer |       | 12	  |                                   | 전체 거래금액(amount)중에서 면세에 해당하는 금액을 설정합니다.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
|     isExpire      | Boolean |   O   |  -	  | 만료여부                              | 만료됨: true <br/> 만료안됨: false                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
|    expireDate     | String  |   O   |  -	  | 만료일시                              | **[ISO8601 포맷]** <br/> UTC: `yyyy-MM-dd'T'HH:mm:ss.SSSZ` <br/> - [ISO8601](https://ko.wikipedia.org/wiki/ISO_8601)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         | 
|     expireAt      | String  |       |  -	  | 만료요청일시                            | **[ISO8601 포맷]** <br/> UTC: `yyyy-MM-dd'T'HH:mm:ss.SSSZ` <br/> - [ISO8601](https://ko.wikipedia.org/wiki/ISO_8601)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         | 
|   mallReserved    | String  |       | 500	 | 상점 정보 전상점 정보 전달용 예비필드<br>         | returnUrl로 redirect되는 시점에 반환 됩니다.<br>JSON string format으로 이용하시기를 권고 드립니다.<br>단, 큰따옴표(")는 이용불가                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | 
|    mallUserId     | String  |       | 20	  | 상점에서 관리하는 사용자 아이디 		              |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | 
|     buyerName     | String  |       | 30	  | 구매자 이름 		                         |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | 
|     buyerTel      | String  |       | 40	  | 구매자 전화번호<br>  		                  | (-) 없이 숫자만 입력                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | 
|    buyerEmail     | String  |       | 60	  | 구매자 이메일 		                        |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
|     useEscrow     | Boolean |       |  -	  | 에스크로 사용여부 <br/> 		                | true: 에스크로 거래 / false: 일반거래(default)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       | 
|     currency      | String  |       |  3	  | 통화설정  		                          | KRW:원화, USD:미화달러, CNY:위안화                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
|    logoImgUrl     | String  |       | 100	 | 로고 이미지의 full URL 전달<br>           | ex) https://youre.site.com/image/logo.jpg <br> 채널별 사이즈 (단위 : pixel) <br>모바일 : width 50 X height 50<br> 웹표준 : width 60 X height 60                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          | 
|     language      | String  |       |  2	  | 결제창 언어 설정                         | EN : 영문 / CN : 중문 / KO : 국문(Default)<br>NICEPAY 결제창 내 다국어를 지원<br>*sample)*<br>lang=EN -> 결제창 영문표기<br>lang=CN -> 결제창 중문표기                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   | 
|   returnCharSet   | String  |   O   | 10	  | 가맹점 서버의 encoding 방식 전달<br> 		     | - utf-8(Default) / euc-kr                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
|     cardQuota     | String  |       | 100	 | 할부개월 설정                           | **[공통]**<br> 결제창에서 선택할 수 있는 할부기간을 제한한다.<br>- ','를 구분자로 할부개월 나열<br>- 일시불은 반드시 "00"으로 설정<br>- 2자리의 할부기간 설정 (3개월일 경우 반드시 '03' 으로 설정)<br>Ex) cardQuota=03 <br><br> **[카드일반+PAYCO+네이버페이]**<br>- 단독 설정 가능<br>- 설명 : 3 개월 할부만 가능하도록 함.<br>최소금액 : 50,000원 이상<br><br>**[카카오페이카드, 삼성페이, SSGPAY]**<br> - 단독 설정 사용불가. 반드시 cardCode와 함께 설정되어야 사용가능.<br> - 할부개월 복수설정 불가                                                                                                                                                                                                                                                                                                                                                  |
|     cardCode      | String  |       | 100	 | 특정카드사 선택옵션                        | **[공통]**<br>이용할 수 있는 카드 리스트를 제한 (코드집-카드코드 참고)<br>- 단독 설정 가능<br><br>**[카드일반]**<br>- ','를 구분자로 카드코드 나열<br>Ex1) cardCode=02<br>-> 국민 카드만 이용할 수 있도록 제한<br>Ex2) cardCode=02,04<br>-> 국민,삼성 카드만 이용할 수 있도록 제한<br><br>**[간편결제]**<br>- 복수설정 불가<br>- 카카오페이와 PAYCO는 각 간편결제에서 카드만 이용 가능함.<br>카카오페이는 카카오Money 이용불가, PAYCO는 페이코포인트 이용불가<br>  ex) cardCode = 06 (카드사 복수설정 불가)<br><br>**간편결제별 이용가능 카드리스트**<br>- 삼성페이 : 비씨,국민,하나(외환),삼성,신한,현대,롯데,NH,하나<br>- 카카오페이: 비씨,국민,하나(외환),삼성,신한,현대,롯데,씨티,NH,하나<br>- PAYCO : 비씨,국민,하나(외환),삼성,신한,현대,롯데,씨티,NH,하나,한미,신세계한미,수협,신협,우리,광주,전북,제주,해외비자,해외마스터,해외JCB,저축은행,은련,KDB산업,카카오뱅크<br>- SSGPAY : 비씨,국민,하나(외환),삼성,신한,현대,롯데,씨티,NH,하나,전북,케이뱅크<br>- 네이버페이 : 신한, 비씨, 국민, NH, 롯데, 삼성, 씨티, 하나, 현대 | 
|    cardShowOpt    | String  |       | 50	  | 카드사별 인증창 호출방식 옵션                  | **카드사별 호출방식 정의하여 카드사로 전달**<br>- '&brvbar;'를 구분자로 카드코드 나열<br>- 카드코드:노출유형&brvbar;카드코드:노출유형<br>ex) CardShowOpt=08:3&brvbar;02:3<br> **[카드코드 및 노출유형 리스트]** <br/> - 1:안심클릭, 2:간편결제, 3:앱카드직접 호출<br>- 이용가능 카드사목록 : 02(국민), 04(삼성), 06(신한), 07(현대), 08(롯데), 12(NH), 15(우리)                                                                                                                                                                                                                                                                                                                                                                                                                                         |
|    vbankHolder    | String  |       |  40  | 가상계좌                              | **[주의사항]** <br/> - 가상계좌 요청 시 필수 값 <br/> - 가맹점 상호명, 사용자명을 입력                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
|  vbankValidHours  | Integer |       |  4   | 가상계좌 유효시간                         | **[주의사항]** <br/> - 시간단위로 입력.<br>- Default 값 D+7일<br>- vbankValidHours와 vbankExpDate가 함께 요청된경우 vbankValidHours가 우선함<br>ex) 10 을 입력하면, 가상계좌 발급후 10시간동안 해당 가상계좌를 이용할 수 있습니다.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
|   vbankExpDate	   | String  |       |      | 가상계좌 입금 만료일                       | **[ISO8601 포맷]** <br/> - 년월일: `YYYY-MM-DD` <br/> - 년월일시분: `yyyy-MM-ddTHH:mm` <br/> - UTC: `yyyy-MM-dd'T'HH:mm:ss.SSSZ` <br/> **[참고]** <br/> - UTC 포맷으로 요청 시 분까지 적용 <br/> - [ISO8601](https://ko.wikipedia.org/wiki/ISO_8601)                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
|     isDigital     | Boolean | 휴대폰결제 |  -   | 컨텐츠 및 실물 여부                       | true: 컨텐츠, false: 실물                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| directReceiptType | String  |       |  20  | 현금영수증 발급 유형                       | **[발급 유형]** <br/> - unPublished(or null) : 미발행<br> - individual : 개인 소득공제용<br> - company : 사업자 지출증빙용                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |   
|  directReceiptNo  | String  |       |  20  | 현금영수증 발행대상 식별정보                   | **[발급 유형에 따른 값]** <br> - directReceiptType 이 individual 또는 company 인 경우 필수 <br/> - '-'없이 숫자만 입력 <br/> * individual: 휴대폰번호 <br/> * company: 사업자번호                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |
|     appScheme     | String  |       | 200  | 모바일 App Scheme 값 (APP 연동인 경우만 사용) | <br>가맹점 독립 APP을 이용하여 개발시 (Webview 연동)<br>카드사를 포함한 제휴사 인증 완료 후 결과에대한 focus가 <br>가맹점 APP으로 돌아오기 위해 설정하는 가맹점 APP의 scheme 값을 설정<br>예) 가맹점 App 스킴이 nicepaysample인 경우<br> appScheme=nicepaysample://                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
|      method       | String  |   O   | 20	  | 결제수단 <br>                         | **[결제수단]** <br/> card : 신용카드 <br> bank : 계좌이체 <br> directCard : 결제창 없이 카드사 바로 노출  <br> vbank : 가상계좌  <br> cellphone : 휴대폰 <br>cardAndEasyPay : 신용카드와 간편결제 노출<br> **[간편결제]** <br/> naverpayCard : 네이버페이-신용카드 전액결제(포인트 이용불가) <br> kakaopay : 카카오페이(카드전액 또는 포인트전액) <br>kakaopayCard : 카카오페이-신용카드 전액결제 <br>kakaopayMoney : 카카오페이-머니 전액결제 <br>samsungpayCard : 삼성페이 카드전액 결제 <br>payco : 페이코 <br>ssgpay : SSGPAY <br> **[주의사항]** <br/> cardAndEasyPay인 경우, 아래 파라미터와 함께 사용불가 <br> - cardCode, cardQuota, shopInterest, quotaInterest                                                                                                                                                                              | 
|        url        | String  |   O   | 250	 | 결제창 호출 URL                        | 결제창 호출을 위한 URL <br/> https://pay.nicepay.co.kr/v1/checkout/pay/{token}/{sessionId} <br/> token: 나이스페이먼츠에서 관리하는 인코딩된 가맹점 ID <br/> sessionId: 가맹점에서 생성하고 요청한 유니크한 Id                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
|   messageSource   | String  |   O   | 10	  | 메시지 출처                            | nicepay: 응답 결과의 출처가 NICEPAY <br/> external: 응답 결과의 출처가 제휴사                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |
|     payMethod     | String  |   O   |  10  | 결제수단                              | **[결제처리된 결제수단]** <br/> card:신용카드, vbank:가상계좌,<br>naverpay=네이버페이, kakaopay=카카오페이, payco=페이코, ssgpay=SSGPAY, samsungpay=삼성페이                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 |