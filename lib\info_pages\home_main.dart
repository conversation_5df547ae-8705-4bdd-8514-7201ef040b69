import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'common_theme.dart';
// ignore: avoid_web_libraries_in_flutter
import 'dart:html' as html;

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(const HomeApp());
}

class HomeApp extends StatelessWidget {
  const HomeApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: '바라 부스 매니저 - Parabara',
      theme: InfoTheme.theme(),
      home: const SafeArea(child: HomePage()),
    );
  }
}

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          children: [
            _buildHeroSection(context),
            _buildFeaturesSection(context),

            _buildLinksSection(context),
            const CompanyFooter(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeroSection(BuildContext context) {
    final text = Theme.of(context).textTheme;
    return Container(
      width: double.infinity,
      decoration: const BoxDecoration(
        gradient: InfoTheme.heroGradient,
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 80),
        child: Center(
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 1200),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: InfoTheme.primaryGradient,
                    borderRadius: BorderRadius.circular(32),
                    boxShadow: InfoTheme.buttonShadow,
                  ),
                  child: const Icon(Icons.storefront, color: Colors.white, size: 56),
                ),
                const SizedBox(height: 32),
                Text(
                  '바라 부스 매니저',
                  style: text.displayMedium?.copyWith(
                    fontWeight: FontWeight.w800,
                    color: InfoTheme.textPrimary,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Text(
                  '창작자를 위한 행사 부스 통합 관리 애플리케이션\n매출, 재고, 고객을 하나의 앱으로 스마트하게 관리하세요',
                  style: text.bodyLarge?.copyWith(
                    fontSize: 18,
                    color: InfoTheme.textSecondary,
                    height: 1.6,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 40),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ElevatedButton.icon(
                      onPressed: () => _openUrl('https://apps.apple.com/app/id6738046369'),
                      icon: const Icon(Icons.apple),
                      label: const Text('App Store'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: InfoTheme.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                      ),
                    ),
                    const SizedBox(width: 16),
                    ElevatedButton.icon(
                      onPressed: () => _openUrl('https://play.google.com/store/apps/details?id=com.parabara.booth_manager'),
                      icon: const Icon(Icons.android),
                      label: const Text('Google Play'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: InfoTheme.secondary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeaturesSection(BuildContext context) {
    final text = Theme.of(context).textTheme;
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 80),
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 1200),
          child: Column(
            children: [
              Text(
                '주요 기능',
                style: text.headlineLarge?.copyWith(
                  fontWeight: FontWeight.w800,
                  color: InfoTheme.textPrimary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                '부스 운영에 필요한 모든 기능을 하나의 앱으로',
                style: text.bodyLarge?.copyWith(
                  color: InfoTheme.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 48),
              LayoutBuilder(
                builder: (context, constraints) {
                  final crossAxisCount = constraints.maxWidth > 900 ? 4 : constraints.maxWidth > 600 ? 2 : 1;
                  return GridView.count(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    crossAxisCount: crossAxisCount,
                    crossAxisSpacing: 24,
                    mainAxisSpacing: 24,
                    childAspectRatio: 1.1,
                    children: [
                      _buildFeatureCard('📊', '매출 관리', '실시간 매출 현황 추적과\n상세한 분석 리포트'),
                      _buildFeatureCard('📦', '재고 관리', '상품별 재고 현황을\n실시간으로 관리'),
                      _buildFeatureCard('👥', '고객 관리', '고객 정보와 구매 이력을\n체계적으로 관리'),
                      _buildFeatureCard('📱', '모바일 최적화', '언제 어디서나 스마트폰으로\n빠르게 확인'),
                    ],
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureCard(String icon, String title, String description) {
    return Container(
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: InfoTheme.surface,
        borderRadius: BorderRadius.circular(20),
        boxShadow: InfoTheme.cardShadow,
        border: Border.all(color: InfoTheme.borderLight),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 64,
            height: 64,
            decoration: BoxDecoration(
              gradient: InfoTheme.primaryGradient,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Center(
              child: Text(
                icon,
                style: const TextStyle(fontSize: 28),
              ),
            ),
          ),
          const SizedBox(height: 20),
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w700,
              color: InfoTheme.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          Text(
            description,
            style: const TextStyle(
              fontSize: 14,
              color: InfoTheme.textSecondary,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }





  Widget _buildLinksSection(BuildContext context) {
    final text = Theme.of(context).textTheme;
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 80),
      decoration: const BoxDecoration(
        color: InfoTheme.surfaceVariant,
      ),
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 1200),
          child: Column(
            children: [
              Text(
                '도움이 필요하신가요?',
                style: text.headlineMedium?.copyWith(
                  fontWeight: FontWeight.w700,
                  color: InfoTheme.textPrimary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 40),
              LayoutBuilder(
                builder: (context, constraints) {
                  final crossAxisCount = constraints.maxWidth > 600 ? 3 : 1;
                  return GridView.count(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    crossAxisCount: crossAxisCount,
                    crossAxisSpacing: 24,
                    mainAxisSpacing: 24,
                    childAspectRatio: 1.5,
                    children: [
                      _buildLinkCard('📋', '개인정보 처리방침', '/privacy-policy'),
                      _buildLinkCard('🗑️', '계정 삭제', '/account-deletion'),
                      _buildLinkCard('🎧', '고객 지원', '/support'),
                    ],
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLinkCard(String icon, String title, String href) {
    return InkWell(
      onTap: () => _openUrl(href),
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: InfoTheme.surface,
          borderRadius: BorderRadius.circular(16),
          boxShadow: InfoTheme.cardShadow,
          border: Border.all(color: InfoTheme.borderLight),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              icon,
              style: const TextStyle(fontSize: 32),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: InfoTheme.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _openUrl(String url) async {
    try {
      // 내부 페이지인지 확인
      if (url.startsWith('/')) {
        // 웹에서는 직접 페이지 이동
        html.window.location.assign(url);
        return;
      }

      // 외부 링크는 새 탭에서 열기
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        // 웹에서는 직접 페이지 이동
        html.window.open(url, '_blank');
      }
    } catch (e) {
      // 웹에서는 직접 페이지 이동
      if (url.startsWith('/')) {
        html.window.location.assign(url);
      } else {
        html.window.open(url, '_blank');
      }
    }
  }
}



