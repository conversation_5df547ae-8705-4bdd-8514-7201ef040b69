/// 바라 부스 매니저 - Firebase Functions 나이스페이 서비스
///
/// Firebase Functions를 통해 나이스페이 API를 안전하게 호출하는 서비스입니다.
/// - 서버 사이드에서 SecretKey 보안 관리
/// - 나이스페이 공식 신 버전 API 사용
/// - 웹훅을 통한 안전한 결제 결과 처리
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'package:cloud_functions/cloud_functions.dart';
import '../utils/logger_utils.dart';

/// 빌링키 발급 결과
class BillingKeyResult {
  final bool success;
  final String? bid;
  final String? tid;
  final String? cardCode;
  final String? cardName;
  final String? authDate;
  final String? errorMessage;

  BillingKeyResult({
    required this.success,
    this.bid,
    this.tid,
    this.cardCode,
    this.cardName,
    this.authDate,
    this.errorMessage,
  });

  factory BillingKeyResult.fromMap(Map<String, dynamic> map) {
    return BillingKeyResult(
      success: map['success'] ?? false,
      bid: map['bid'],
      tid: map['tid'],
      cardCode: map['cardCode'],
      cardName: map['cardName'],
      authDate: map['authDate'],
      errorMessage: map['errorMessage'],
    );
  }
}

/// 빌링 승인 결과
class BillingPaymentResult {
  final bool success;
  final String? tid;
  final String? orderId;
  final int? amount;
  final String? status;
  final String? paidAt;
  final String? receiptUrl;
  final Map<String, dynamic>? card;
  final String? errorMessage;

  BillingPaymentResult({
    required this.success,
    this.tid,
    this.orderId,
    this.amount,
    this.status,
    this.paidAt,
    this.receiptUrl,
    this.card,
    this.errorMessage,
  });

  factory BillingPaymentResult.fromMap(Map<String, dynamic> map) {
    return BillingPaymentResult(
      success: map['success'] ?? false,
      tid: map['tid'],
      orderId: map['orderId'],
      amount: map['amount'],
      status: map['status'],
      paidAt: map['paidAt'],
      receiptUrl: map['receiptUrl'],
      card: map['card'],
      errorMessage: map['errorMessage'],
    );
  }
}

/// Firebase Functions 나이스페이 서비스
class FirebaseNicePayService {
  static const String _tag = 'FirebaseNicePayService';

  final FirebaseFunctions _functions = FirebaseFunctions.instance;

  /// 빌링키 발급
  /// 
  /// [cardNo] 카드번호 (숫자만)
  /// [expYear] 유효기간 년 (YY)
  /// [expMonth] 유효기간 월 (MM)
  /// [idNo] 생년월일 (YYMMDD) 또는 사업자번호
  /// [cardPw] 카드 비밀번호 앞 2자리
  /// [buyerName] 구매자 이름
  /// [buyerEmail] 구매자 이메일
  /// [buyerTel] 구매자 전화번호
  Future<BillingKeyResult> createBillingKey({
    required String cardNo,
    required String expYear,
    required String expMonth,
    required String idNo,
    required String cardPw,
    String? buyerName,
    String? buyerEmail,
    String? buyerTel,
  }) async {
    try {
      LoggerUtils.logInfo('Firebase Functions 빌링키 발급 시작', tag: _tag);

      final callable = _functions.httpsCallable('createBillingKey');
      final result = await callable.call({
        'cardNo': cardNo,
        'expYear': expYear,
        'expMonth': expMonth,
        'idNo': idNo,
        'cardPw': cardPw,
        'buyerName': buyerName,
        'buyerEmail': buyerEmail,
        'buyerTel': buyerTel,
      });

      LoggerUtils.logInfo('Firebase Functions 빌링키 발급 응답: ${result.data}', tag: _tag);

      return BillingKeyResult.fromMap(result.data);
    } catch (e) {
      LoggerUtils.logError('Firebase Functions 빌링키 발급 오류', tag: _tag, error: e);
      return BillingKeyResult(
        success: false,
        errorMessage: e.toString(),
      );
    }
  }

  /// 빌링 승인 (정기결제)
  /// 
  /// [bid] 빌링키
  /// [amount] 결제 금액
  /// [goodsName] 상품명
  /// [buyerName] 구매자 이름
  /// [buyerEmail] 구매자 이메일
  /// [buyerTel] 구매자 전화번호
  Future<BillingPaymentResult> processBillingPayment({
    required String bid,
    required int amount,
    required String goodsName,
    String? buyerName,
    String? buyerEmail,
    String? buyerTel,
  }) async {
    try {
      LoggerUtils.logInfo('Firebase Functions 빌링 승인 시작: $bid, $amount원', tag: _tag);

      final callable = _functions.httpsCallable('processBillingPayment');
      final result = await callable.call({
        'bid': bid,
        'amount': amount,
        'goodsName': goodsName,
        'buyerName': buyerName,
        'buyerEmail': buyerEmail,
        'buyerTel': buyerTel,
      });

      LoggerUtils.logInfo('Firebase Functions 빌링 승인 응답: ${result.data}', tag: _tag);

      return BillingPaymentResult.fromMap(result.data);
    } catch (e) {
      LoggerUtils.logError('Firebase Functions 빌링 승인 오류', tag: _tag, error: e);
      return BillingPaymentResult(
        success: false,
        errorMessage: e.toString(),
      );
    }
  }

  /// API 키 유효성 테스트
  Future<bool> testApiConnection() async {
    try {
      LoggerUtils.logInfo('Firebase Functions API 연결 테스트 시작', tag: _tag);

      // 테스트용 더미 카드로 빌링키 발급 시도 (실제로는 실행되지 않음)
      await createBillingKey(
        cardNo: '1234567890123456',
        expYear: '25',
        expMonth: '12',
        idNo: '800101',
        cardPw: '12',
        buyerName: 'API 테스트',
        buyerEmail: '<EMAIL>',
        buyerTel: '01012345678',
      );

      // 연결 자체가 성공하면 true (실제 빌링키 발급 실패는 상관없음)
      LoggerUtils.logInfo('Firebase Functions API 연결 테스트 완료', tag: _tag);
      return true;
    } catch (e) {
      LoggerUtils.logError('Firebase Functions API 연결 테스트 실패', tag: _tag, error: e);
      return false;
    }
  }
}
