/// 바라 부스 매니저 - 구독 관리 화면
///
/// 나이스페이 for start 빌링을 사용한 구독 관리 화면입니다.
/// - 빌링키 발급 (직접 입력 방식)
/// - 빌링키 발급 (웹뷰 방식)
/// - 정기결제 테스트
/// - 구독 상태 관리
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../../services/nice_pay_service.dart';
import '../../widgets/app_bar_styles.dart';
import '../../utils/toast_utils.dart';
import '../../utils/logger_utils.dart';
import 'nice_webview_billing_screen.dart';
import '../../utils/currency_utils.dart';
import 'card_registration_screen.dart';
import 'firebase_card_registration_screen.dart';

/// 구독 관리 화면
class SubscriptionManagementScreen extends ConsumerStatefulWidget {
  const SubscriptionManagementScreen({super.key});

  @override
  ConsumerState<SubscriptionManagementScreen> createState() => _SubscriptionManagementScreenState();
}

class _SubscriptionManagementScreenState extends ConsumerState<SubscriptionManagementScreen> {
  static const String _tag = 'SubscriptionManagementScreen';

  final NicePayService _nicePayService = NicePayService();
  final Uuid _uuid = const Uuid();

  // 테스트용 데이터
  String? _billingKey;
  String? _tid;
  NiceCardInfo? _cardInfo;
  List<NicePaymentResult> _paymentHistory = [];

  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    LoggerUtils.logInfo('구독 관리 화면 초기화', tag: _tag);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Builder(builder: (ctx) => Text('구독 관리 (테스트)', style: AppBarStyles.of(ctx))),
        centerTitle: true,
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 테스트 안내
              _buildTestNotice(),
              const SizedBox(height: 24),
              
              // 빌링키 상태
              _buildBillingKeyStatus(),
              const SizedBox(height: 24),
              
              // 액션 버튼들
              _buildActionButtons(),
              const SizedBox(height: 24),
              
              // 결제 내역
              if (_paymentHistory.isNotEmpty) ...[
                _buildPaymentHistory(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTestNotice() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info, color: Colors.blue.shade700),
              const SizedBox(width: 8),
              Text(
                '나이스페이 for start 테스트 환경',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '• 실제 결제가 발생하지 않습니다\n• 테스트 카드로만 결제 가능합니다\n• 가맹점 ID: nictest04m',
            style: TextStyle(
              fontSize: 12,
              color: Colors.blue.shade700,
            ),
          ),
          const SizedBox(height: 12),
          ElevatedButton.icon(
            onPressed: _testApiKeys,
            icon: Icon(Icons.key, size: 16),
            label: Text('API 키 테스트'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade100,
              foregroundColor: Colors.blue.shade700,
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              minimumSize: Size(0, 32),
              textStyle: TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBillingKeyStatus() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _billingKey != null ? Icons.credit_card : Icons.credit_card_off,
                color: _billingKey != null ? Colors.green : Colors.grey,
              ),
              const SizedBox(width: 8),
              Text(
                '카드 등록 상태',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          if (_billingKey != null && _cardInfo != null) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '✅ 카드가 등록되었습니다',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.green.shade700,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text('카드사: ${_cardInfo!.cardName}'),
                  Text('카드타입: ${_cardInfo!.cardTypeText}'),
                  Text('매입사: ${_cardInfo!.acquCardName}'),
                  const SizedBox(height: 8),
                  Text(
                    '빌링키: ${_billingKey!.substring(0, 8)}...',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ] else ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Text(
                '카드가 등록되지 않았습니다.\n정기결제를 사용하려면 먼저 카드를 등록해주세요.',
                style: TextStyle(
                  color: Colors.grey.shade700,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        // 카드 등록/변경 버튼 (직접 입력 방식)
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _isProcessing ? null : _registerCard,
            icon: Icon(_billingKey != null ? Icons.edit : Icons.add_card),
            label: Text(_billingKey != null ? '카드 변경 (직접 입력)' : '카드 등록 (직접 입력)'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),

        const SizedBox(height: 12),

        // 웹뷰 빌링 테스트 버튼
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _isProcessing ? null : _registerCardWebview,
            icon: const Icon(Icons.web),
            label: const Text('웹뷰 빌링 테스트'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),

        const SizedBox(height: 12),

        // Firebase Functions 빌링 테스트 버튼
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _isProcessing ? null : _registerCardFirebase,
            icon: const Icon(Icons.cloud),
            label: const Text('Firebase Functions 빌링 (권장)'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 12),
        
        // 정기결제 테스트 버튼
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: (_billingKey != null && !_isProcessing) ? _testPayment : null,
            icon: const Icon(Icons.payment),
            label: const Text('정기결제 테스트 (4,900원)'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.amber,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),

        const SizedBox(height: 12),

        // 🚨 결제 취소 버튼 (긴급 추가)
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: !_isProcessing ? _showCancelPaymentDialog : null,
            icon: const Icon(Icons.cancel),
            label: const Text('🚨 결제 취소 (긴급)'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
        
        if (_billingKey != null) ...[
          const SizedBox(height: 12),
          
          // 빌링키 삭제 버튼
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: _isProcessing ? null : _deleteBillingKey,
              icon: const Icon(Icons.delete),
              label: const Text('카드 등록 해제'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.red,
                side: const BorderSide(color: Colors.red),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildPaymentHistory() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '결제 내역',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          ...(_paymentHistory.reversed.take(5).map((payment) => _buildPaymentItem(payment))),
          
          if (_paymentHistory.length > 5) ...[
            const SizedBox(height: 8),
            Text(
              '총 ${_paymentHistory.length}건의 결제 내역이 있습니다.',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPaymentItem(NicePaymentResult payment) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: payment.success ? Colors.green.shade50 : Colors.red.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: payment.success ? Colors.green.shade200 : Colors.red.shade200,
        ),
      ),
      child: Row(
        children: [
          Icon(
            payment.success ? Icons.check_circle : Icons.error,
            color: payment.success ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  payment.success 
                      ? '결제 성공 - ${CurrencyUtils.formatCurrency(payment.amount ?? 0)}'
                      : '결제 실패',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: payment.success ? Colors.green.shade700 : Colors.red.shade700,
                  ),
                ),
                if (payment.authDate != null)
                  Text(
                    '승인일시: ${payment.authDate}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                if (!payment.success && payment.errorMessage != null)
                  Text(
                    payment.errorMessage!,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.red.shade600,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 카드 등록
  Future<void> _registerCard() async {
    setState(() {
      _isProcessing = true;
    });

    try {
      LoggerUtils.logInfo('카드 등록 시작', tag: _tag);

      // 카드 등록 화면으로 이동
      final result = await Navigator.of(context).push<NiceBillingKeyResult>(
        MaterialPageRoute(
          builder: (context) => CardRegistrationScreen(
            customerName: '테스트 사용자',
            customerEmail: '<EMAIL>',
            customerTel: '01012345678',
            onResult: (result) {
              // 결과는 pop에서 처리
            },
          ),
        ),
      );

      if (result != null && result.success) {
        setState(() {
          _billingKey = result.billingKey;
          _tid = result.tid;
          _cardInfo = result.cardInfo;
        });

        ToastUtils.showSuccess(context, '카드가 성공적으로 등록되었습니다!');
        LoggerUtils.logInfo('카드 등록 성공: ${result.billingKey}', tag: _tag);
      } else if (result != null) {
        ToastUtils.showError(context, result.errorMessage ?? '카드 등록에 실패했습니다.');
        LoggerUtils.logError('카드 등록 실패: ${result.errorMessage}', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('카드 등록 중 오류', tag: _tag, error: e);
      ToastUtils.showError(context, '카드 등록 중 오류가 발생했습니다.');
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  /// 카드 등록 (웹뷰 방식)
  Future<void> _registerCardWebview() async {
    setState(() {
      _isProcessing = true;
    });

    try {
      LoggerUtils.logInfo('카드 등록 시작 (웹뷰)', tag: _tag);

      // 웹뷰 빌링 화면으로 이동
      final result = await Navigator.of(context).push<NiceBillingKeyResult>(
        MaterialPageRoute(
          builder: (context) => NiceWebviewBillingScreen(
            customerName: '테스트 사용자',
            customerEmail: '<EMAIL>',
            customerTel: '01012345678',
            onResult: (result) {
              Navigator.of(context).pop(result);
            },
          ),
        ),
      );

      if (result != null && result.success) {
        setState(() {
          _billingKey = result.billingKey;
          _tid = result.tid;
          _cardInfo = result.cardInfo;
        });

        ToastUtils.showSuccess(context, '웹뷰를 통해 카드가 성공적으로 등록되었습니다!');
        LoggerUtils.logInfo('웹뷰 카드 등록 성공: ${result.billingKey}', tag: _tag);
      } else if (result != null) {
        ToastUtils.showError(context, result.errorMessage ?? '웹뷰 카드 등록에 실패했습니다.');
        LoggerUtils.logError('웹뷰 카드 등록 실패: ${result.errorMessage}', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('웹뷰 카드 등록 중 오류', tag: _tag, error: e);
      ToastUtils.showError(context, '웹뷰 카드 등록 중 오류가 발생했습니다.');
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  /// 카드 등록 (Firebase Functions 방식)
  Future<void> _registerCardFirebase() async {
    setState(() {
      _isProcessing = true;
    });

    try {
      LoggerUtils.logInfo('카드 등록 시작 (Firebase Functions)', tag: _tag);

      // 카드 등록 화면으로 이동
      final result = await Navigator.of(context).push<NiceBillingKeyResult>(
        MaterialPageRoute(
          builder: (context) => FirebaseCardRegistrationScreen(
            customerName: '테스트 사용자',
            customerEmail: '<EMAIL>',
            customerTel: '01012345678',
          ),
        ),
      );

      if (result != null && result.success) {
        setState(() {
          _billingKey = result.billingKey;
          _tid = result.tid;
          _cardInfo = result.cardInfo;
        });

        ToastUtils.showSuccess(context, 'Firebase Functions를 통해 카드가 성공적으로 등록되었습니다!');
        LoggerUtils.logInfo('Firebase Functions 카드 등록 성공: ${result.billingKey}', tag: _tag);
      } else if (result != null) {
        ToastUtils.showError(context, result.errorMessage ?? 'Firebase Functions 카드 등록에 실패했습니다.');
        LoggerUtils.logError('Firebase Functions 카드 등록 실패: ${result.errorMessage}', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('Firebase Functions 카드 등록 중 오류', tag: _tag, error: e);
      ToastUtils.showError(context, 'Firebase Functions 카드 등록 중 오류가 발생했습니다.');
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  /// 정기결제 테스트
  Future<void> _testPayment() async {
    if (_billingKey == null) {
      ToastUtils.showError(context, '먼저 카드를 등록해주세요.');
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      LoggerUtils.logInfo('정기결제 테스트 시작', tag: _tag);

      // 로딩 다이얼로그 표시
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('결제를 처리하고 있습니다...'),
            ],
          ),
        ),
      );

      // 정기결제 실행
      final result = await _nicePayService.executePayment(
        billingKey: _billingKey!,
        tid: _tid!,
        amount: 4900, // 프로 플랜 월 요금
        goodsName: '바라 부스 매니저 프로 플랜',
        orderId: _uuid.v4(),
      );

      // 로딩 다이얼로그 닫기
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 결제 내역에 추가
      setState(() {
        _paymentHistory.add(result);
      });

      if (result.success) {
        ToastUtils.showSuccess(context, '결제가 성공적으로 완료되었습니다!');
        LoggerUtils.logInfo('정기결제 성공: ${result.tid}', tag: _tag);
      } else {
        ToastUtils.showError(context, result.errorMessage ?? '결제에 실패했습니다.');
        LoggerUtils.logError('정기결제 실패: ${result.errorMessage}', tag: _tag);
      }
    } catch (e) {
      // 로딩 다이얼로그 닫기
      if (mounted) {
        Navigator.of(context).pop();
      }

      LoggerUtils.logError('정기결제 중 오류', tag: _tag, error: e);
      ToastUtils.showError(context, '결제 중 오류가 발생했습니다.');
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  /// 빌링키 삭제
  Future<void> _deleteBillingKey() async {
    if (_billingKey == null) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('카드 등록 해제'),
        content: Text('등록된 카드를 해제하시겠습니까?\n정기결제가 중단됩니다.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text('취소'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text('해제'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      LoggerUtils.logInfo('빌링키 삭제 시작', tag: _tag);

      final success = await _nicePayService.deleteBillingKey(_billingKey!);

      if (success) {
        setState(() {
          _billingKey = null;
          _tid = null;
          _cardInfo = null;
          _paymentHistory.clear();
        });

        ToastUtils.showSuccess(context, '카드 등록이 해제되었습니다.');
        LoggerUtils.logInfo('빌링키 삭제 성공', tag: _tag);
      } else {
        ToastUtils.showError(context, '카드 등록 해제에 실패했습니다.');
        LoggerUtils.logError('빌링키 삭제 실패', tag: _tag);
      }
    } catch (e) {
      LoggerUtils.logError('빌링키 삭제 중 오류', tag: _tag, error: e);
      ToastUtils.showError(context, '카드 등록 해제 중 오류가 발생했습니다.');
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  /// API 키 테스트
  Future<void> _testApiKeys() async {
    try {
      // 로딩 다이얼로그 표시
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('API 키를 테스트하고 있습니다...'),
            ],
          ),
        ),
      );

      final result = await _nicePayService.testApiKeys();

      // 로딩 다이얼로그 닫기
      if (mounted) {
        Navigator.of(context).pop();
      }

      if (result.isValid) {
        ToastUtils.showSuccess(context, result.message);
      } else {
        ToastUtils.showError(context, result.message);
      }
    } catch (e) {
      // 로딩 다이얼로그 닫기
      if (mounted) {
        Navigator.of(context).pop();
      }

      ToastUtils.showError(context, 'API 키 테스트 중 오류가 발생했습니다.');
      LoggerUtils.logError('API 키 테스트 중 오류', tag: _tag, error: e);
    }
  }

  /// 🚨 결제 취소 다이얼로그 표시 (긴급)
  void _showCancelPaymentDialog() {
    final tidController = TextEditingController();
    final orderIdController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning, color: Colors.red),
            SizedBox(width: 8),
            Text('🚨 결제 취소 (긴급)'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '실제 결제가 발생한 경우 즉시 취소하세요!\n\n'
              '취소하려는 결제의 정보를 입력해주세요:',
              style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: tidController,
              decoration: const InputDecoration(
                labelText: 'TID (거래번호)',
                hintText: 'nictest04m...',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: orderIdController,
              decoration: const InputDecoration(
                labelText: '주문번호 (OrderID)',
                hintText: 'UUID 형식',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              '💡 TID와 주문번호는 결제 로그에서 확인할 수 있습니다.',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _cancelPayment(tidController.text.trim(), orderIdController.text.trim());
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('결제 취소', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  /// 🚨 결제 취소 실행 (긴급)
  Future<void> _cancelPayment(String tid, String orderId) async {
    if (tid.isEmpty || orderId.isEmpty) {
      ToastUtils.showError(context, 'TID와 주문번호를 모두 입력해주세요.');
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      LoggerUtils.logInfo('🚨 긴급 결제 취소 시작: TID=$tid, OrderID=$orderId', tag: _tag);

      // 로딩 다이얼로그 표시
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('결제 취소 중...'),
            ],
          ),
        ),
      );

      // 결제 취소 API 호출
      final result = await _nicePayService.cancelPayment(
        tid: tid,
        orderId: orderId,
        reason: '사용자 요청에 의한 긴급 취소',
      );

      // 로딩 다이얼로그 닫기
      if (mounted) {
        Navigator.of(context).pop();
      }

      if (result['success'] == true) {
        LoggerUtils.logInfo('🎉 결제 취소 성공!', tag: _tag);
        ToastUtils.showSuccess(context, '결제가 성공적으로 취소되었습니다!');

        // 성공 다이얼로그 표시
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Row(
              children: [
                Icon(Icons.check_circle, color: Colors.green),
                SizedBox(width: 8),
                Text('취소 완료'),
              ],
            ),
            content: Text(
              '결제가 성공적으로 취소되었습니다!\n\n'
              'TID: $tid\n'
              '취소 사유: 사용자 요청에 의한 긴급 취소',
            ),
            actions: [
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('확인'),
              ),
            ],
          ),
        );
      } else {
        LoggerUtils.logError('결제 취소 실패: ${result['error']}', tag: _tag);
        ToastUtils.showError(context, '결제 취소 실패: ${result['error']}');
      }
    } catch (e) {
      // 로딩 다이얼로그 닫기
      if (mounted) {
        Navigator.of(context).pop();
      }

      LoggerUtils.logError('결제 취소 중 오류: $e', tag: _tag);
      ToastUtils.showError(context, '결제 취소 중 오류가 발생했습니다: $e');
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }
}
