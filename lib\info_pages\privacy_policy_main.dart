import 'package:flutter/material.dart';
import 'common_theme.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(const PrivacyPolicyApp());
}

class PrivacyPolicyApp extends StatelessWidget {
  const PrivacyPolicyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: '개인정보 처리방침 - 바라부스매니저',
      theme: InfoTheme.theme(),
      home: const SafeArea(child: PrivacyPolicyPage()),
    );
  }
}

class PrivacyPolicyPage extends StatelessWidget {
  const PrivacyPolicyPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(
        child: Column(
          children: [
            // 헤더 섹션
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 60),
              decoration: const BoxDecoration(
                color: Color(0xFFF8F9FA),
              ),
              child: Center(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 800),
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: const Color(0xFF2563EB).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: const Icon(Icons.privacy_tip_outlined, color: Color(0xFF2563EB), size: 40),
                      ),
                      const SizedBox(height: 24),
                      Text(
                        '개인정보 처리방침',
                        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.w800,
                          color: const Color(0xFF1F2937),
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        '바라부스매니저는 개인정보보호법에 따라 이용자의 개인정보 보호 및 권익을 보호하고자\n다음과 같은 처리방침을 두고 있습니다.',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: const Color(0xFF6B7280),
                          height: 1.6,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          color: const Color(0xFF2563EB).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          '시행일: 2025년 8월 20일',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: const Color(0xFF2563EB),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            // 콘텐츠 섹션
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 60),
              child: Center(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 800),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildSection('1. 수집하는 개인정보의 항목', [
                        _buildInfoCard('필수 수집 정보', [
                          '이메일 주소: 회원가입, 로그인, 본인 확인',
                          '비밀번호: 계정 보안 (암호화 저장)',
                        ]),
                        const SizedBox(height: 16),
                        _buildInfoCard('선택 수집 정보', [
                          '전화번호: 계정 복구, 추가 인증',
                          '※ 선택정보 미동의 시: 계정 복구 기능 이용 제한',
                        ]),
                        const SizedBox(height: 16),
                        _buildInfoCard('결제 관련 정보 (구독 시)', [
                          '카드번호, 유효기간, 생년월일, 카드 비밀번호 앞 2자리',
                          '구매자명, 구매자 이메일, 구매자 전화번호',
                          '결제내역: 결제금액, 결제일시, 승인번호',
                        ]),
                        const SizedBox(height: 16),
                        _buildInfoCard('서비스 이용 데이터', [
                          '행사정보: 행사명, 일정 등 (사용자 입력)',
                          '상품정보: 상품명, 가격, 재고 등 (사용자 입력)',
                          '판매자 닉네임 등 (사용자 입력)',
                          '매출기록: 판매 내역, 통계 등 (사용자 입력)',
                        ]),
                      ]),
                      
                      const SizedBox(height: 40),
                      _buildSection('2. 개인정보의 수집·이용 목적', [
                        _buildBulletList([
                          '회원 가입, 본인 확인 및 회원제 서비스 제공',
                          '결제 처리, 정기 구독 관리 및 요금 정산',
                          '고객 지원, 문의 응대 및 서비스 안내',
                          '서비스 개선 및 맞춤형 서비스 제공',
                          '부정 이용 방지 및 서비스 안정성 확보',
                        ]),
                      ]),
                      
                      const SizedBox(height: 40),
                      _buildSection('3. 개인정보의 처리 및 보유 기간', [
                        _buildTable([
                          ['구분', '보유기간', '근거'],
                          ['회원정보', '회원 탈퇴 시까지', '서비스 제공을 위한 필수 정보'],
                          ['결제정보', '결제 완료 후 5년', '전자상거래 등에서의 소비자보호에 관한 법률'],
                          ['미이용 계정', '3년 후 삭제', '개인정보보호법 (사전 통지 후 삭제)'],
                          ['서버 데이터', '구독 중단 후 6개월', '서비스 제공 목적 달성 시'],
                        ]),
                      ]),
                      
                      const SizedBox(height: 40),
                      _buildSection('4. 개인정보 처리업무의 위탁', [
                        const Text('서비스 제공을 위해 다음과 같이 개인정보 처리업무를 위탁하고 있습니다:',
                          style: TextStyle(color: Color(0xFF6B7280), height: 1.5)),
                        const SizedBox(height: 16),
                        _buildTable([
                          ['수탁업체', '위탁업무', '개인정보 항목', '보유기간'],
                          ['Google LLC', 'Firebase 클라우드 서비스', '회원정보, 서비스 이용기록', '회원 탈퇴 시까지'],
                          ['나이스페이㈜', '결제 대행 서비스', '카드정보, 구매자정보', '결제 완료 후 5년'],
                        ]),
                        const SizedBox(height: 16),
                        const Text('위탁업체는 개인정보보호법에 따라 개인정보를 안전하게 처리하며, 위탁계약 시 개인정보 보호 관련 지시 엄수, 개인정보에 관한 비밀유지, 제3자 제공 금지 및 사고 시 책임부담 등을 명확히 규정하고 있습니다.',
                          style: TextStyle(color: Color(0xFF6B7280), height: 1.5)),
                      ]),

                      const SizedBox(height: 40),
                      _buildSection('5. 개인정보의 제3자 제공', [
                        const Text('원칙적으로 이용자의 동의 없이 개인정보를 제3자에게 제공하지 않습니다. 다만, 법령에 의한 경우에 한해 제공할 수 있습니다:',
                          style: TextStyle(color: Color(0xFF6B7280), height: 1.5)),
                        const SizedBox(height: 16),
                        _buildBulletList([
                          '수사기관의 수사목적으로 법령에 정해진 절차와 방법에 따라 요구하는 경우',
                          '통계작성, 학술연구 등의 목적을 위해 특정 개인을 알아볼 수 없는 형태로 제공하는 경우',
                          '기타 법률에 의해 요구되는 경우',
                        ]),
                      ]),
                      
                      const SizedBox(height: 40),
                      _buildSection('6. 개인정보의 파기 절차 및 방법', [
                        const Text('개인정보 보유기간의 경과, 처리목적 달성 등 개인정보가 불필요하게 되었을 때에는 지체없이 해당 개인정보를 파기합니다.',
                          style: TextStyle(color: Color(0xFF6B7280), height: 1.5)),
                        const SizedBox(height: 16),
                        _buildInfoCard('파기 절차', [
                          '이용자가 입력한 정보는 목적 달성 후 별도의 DB에 옮겨져 내부 방침 및 기타 관련 법령에 따라 일정기간 저장된 후 혹은 즉시 파기됩니다.',
                          '이 때, DB로 옮겨진 개인정보는 법률에 의한 경우가 아니고서는 다른 목적으로 이용되지 않습니다.',
                        ]),
                        const SizedBox(height: 16),
                        _buildInfoCard('파기 방법', [
                          '전자적 파일 형태: 기록을 재생할 수 없도록 로우레벨 포맷(Low Level Format) 등의 방법을 이용하여 파기',
                          '종이 문서: 분쇄기로 분쇄하거나 소각하여 파기',
                          '데이터베이스: 완전삭제 및 복구 불가능한 방식으로 처리',
                        ]),
                      ]),

                      const SizedBox(height: 40),
                      _buildSection('7. 개인정보의 안전성 확보 조치', [
                        _buildBulletList([
                          '개인정보 암호화: 비밀번호, 카드정보 등 중요 정보 암호화 저장',
                          '접근 통제: 개인정보 처리시스템 접근권한 제한 및 관리',
                          '접속 기록 보관: 개인정보 처리 시스템 접속 기록 보관 및 위변조 방지',
                          '보안 프로그램: 백신 소프트웨어 설치 및 주기적 업데이트',
                          '물리적 보안: 서버실 출입 통제 및 보안 장치 설치',
                        ]),
                      ]),
                      
                      const SizedBox(height: 40),
                      _buildSection('8. 이용자의 권리와 행사방법', [
                        const Text('이용자는 언제든지 다음 권리를 행사할 수 있습니다:',
                          style: TextStyle(color: Color(0xFF6B7280), height: 1.5)),
                        const SizedBox(height: 16),
                        _buildBulletList([
                          '개인정보 열람권: 처리 현황, 처리 목적 등에 대한 열람 요구',
                          '정정·삭제권: 잘못된 정보의 수정이나 삭제 요구',
                          '처리정지권: 개인정보 처리 중단 요구',
                          '손해배상청구권: 개인정보 침해로 인한 피해 배상 청구',
                        ]),
                        const SizedBox(height: 16),
                        _buildContactCard(),
                        const SizedBox(height: 16),
                        _buildInfoCard('개인정보 침해신고센터 등 외부기관', [
                          '개인정보보호위원회: privacy.go.kr / 국번없이 182',
                          '개인정보 침해신고센터: privacy.go.kr / 국번없이 182',
                          '대검찰청 사이버범죄수사단: www.spo.go.kr / 02-3480-3573',
                          '경찰청 사이버테러대응센터: www.netan.go.kr / 국번없이 182',
                        ]),
                      ]),
                      
                      const SizedBox(height: 40),
                      _buildSection('9. 개인정보보호책임자', [
                        _buildResponsiblePersonCard(),
                      ]),

                      const SizedBox(height: 40),
                      _buildSection('10. 개인정보 처리방침의 변경', [
                        _buildBulletList([
                          '본 개인정보처리방침은 시행일로부터 적용됩니다',
                          '법령·정책 또는 보안기술의 변경에 따라 내용의 추가·삭제 및 수정이 있을 경우 변경사항의 시행 7일 전부터 공지사항을 통하여 고지합니다',
                          '수집하는 개인정보의 항목, 이용목적의 변경 등 이용자 권리의 중대한 변경이 발생할 때에는 최소 30일 전에 공지하며, 필요시 이용자 동의를 다시 받을 수 있습니다',
                        ]),
                      ]),
                    ],
                  ),
                ),
              ),
            ),
            
            const CompanyFooter(),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w700,
            color: Color(0xFF1F2937),
          ),
        ),
        const SizedBox(height: 16),
        ...children,
      ],
    );
  }

  Widget _buildInfoCard(String title, List<String> items) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE5E7EB)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 12),
          ...items.map((item) => Padding(
            padding: const EdgeInsets.only(bottom: 6),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('• ', style: TextStyle(color: Color(0xFF2563EB), fontWeight: FontWeight.w600)),
                Expanded(
                  child: Text(
                    item,
                    style: const TextStyle(
                      color: Color(0xFF6B7280),
                      height: 1.4,
                    ),
                  ),
                ),
              ],
            ),
          )).toList(),
        ],
      ),
    );
  }

  Widget _buildBulletList(List<String> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: items.map((item) => Padding(
        padding: const EdgeInsets.only(bottom: 8),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('• ', style: TextStyle(color: Color(0xFF2563EB), fontWeight: FontWeight.w600)),
            Expanded(
              child: Text(
                item,
                style: const TextStyle(
                  color: Color(0xFF6B7280),
                  height: 1.4,
                ),
              ),
            ),
          ],
        ),
      )).toList(),
    );
  }

  Widget _buildTable(List<List<String>> data) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: const Color(0xFFE5E7EB)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: data.asMap().entries.map((entry) {
          final index = entry.key;
          final row = entry.value;
          final isHeader = index == 0;

          return Container(
            decoration: BoxDecoration(
              color: isHeader ? const Color(0xFFF8F9FA) : Colors.white,
              border: index > 0 ? const Border(top: BorderSide(color: Color(0xFFE5E7EB))) : null,
            ),
            child: Row(
              children: row.asMap().entries.map((cellEntry) {
                final cellIndex = cellEntry.key;
                final cell = cellEntry.value;

                return Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: cellIndex > 0 ? const BoxDecoration(
                      border: Border(left: BorderSide(color: Color(0xFFE5E7EB))),
                    ) : null,
                    child: Text(
                      cell,
                      style: TextStyle(
                        fontWeight: isHeader ? FontWeight.w600 : FontWeight.w400,
                        color: isHeader ? const Color(0xFF1F2937) : const Color(0xFF6B7280),
                        fontSize: 12,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildContactCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF2563EB).withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF2563EB).withOpacity(0.2)),
      ),
      child: const Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('권리 행사 방법', style: TextStyle(fontWeight: FontWeight.w600, color: Color(0xFF1F2937))),
          SizedBox(height: 12),
          Text('• 앱 내 설정 메뉴에서 직접 처리', style: TextStyle(color: Color(0xFF6B7280))),
          Text('• 이메일: <EMAIL>', style: TextStyle(color: Color(0xFF6B7280))),
          Text('• 전화: 010-2391-4308', style: TextStyle(color: Color(0xFF6B7280))),
          Text('• 처리기간: 요청일로부터 10일 이내', style: TextStyle(color: Color(0xFF6B7280))),
        ],
      ),
    );
  }

  Widget _buildResponsiblePersonCard() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: const Color(0xFF2563EB).withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF2563EB).withOpacity(0.2)),
      ),
      child: const Column(
        children: [
          Icon(Icons.person, color: Color(0xFF2563EB), size: 32),
          SizedBox(height: 12),
          Text('개인정보보호책임자', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w700, color: Color(0xFF1F2937))),
          SizedBox(height: 16),
          Text('성명: 권태영', style: TextStyle(color: Color(0xFF6B7280))),
          Text('직책: 대표', style: TextStyle(color: Color(0xFF6B7280))),
          Text('연락처: 010-2391-4308', style: TextStyle(color: Color(0xFF6B7280))),
          Text('이메일: <EMAIL>', style: TextStyle(color: Color(0xFF6B7280))),
          SizedBox(height: 12),
          Text('개인정보 처리와 관련한 불만처리 및 피해구제를 위해\n언제든지 연락해 주시기 바랍니다.',
            style: TextStyle(color: Color(0xFF6B7280), fontSize: 12), textAlign: TextAlign.center),
        ],
      ),
    );
  }
}
