# 코드집

[HTTP-상태코드](#http-상태코드) | [카드코드](#카드코드) | [은행코드](#은행코드) | [API 응답코드](#API-응답코드) | [더 알아보기](#더-알아보기)

<!--
[HTTP-상태코드](#http-상태코드) | [카드코드](#카드코드) | [은행코드](#은행코드) | [JS SDK 응답코드](#js-sdk-응답코드) | [API 응답코드](#api-응답코드) | [더 알아보기](#더-알아보기)
-->

<br>

## HTTP 상태코드
|http status|설명|
|:---:|:--|
|200|OK, 요청이 정상 처리된 경우|
|401|Unauthorized, SecretKey 또는 토큰 없거나 인증에 실패한 경우|
|403|Forbidden,|
|404|Not Found, 요청한 리소스가 존재하지 않는 경우|
|405|Method Not Allowed, URI에서 지원하지 않는 method로 요청한경우|

<br>
  
## 카드코드
| 코드 | 한글         | 영문         | 결제창                    | 대표 웹 사이트                   |
|:------:|:--------------|:--------------|:---------------------------:|:----------------------------------|
| 01    | 비씨         | BC           | O                         | https://www.bccard.com/          |
| 02    | KB국민       | KB Kookmin   | O                         | https://card.kbcard.com/         |
| 03    | 하나(외환)   | KEB Bank     | O                         | https://www.hanacard.co.kr/      |
| 04    | 삼성         | SAMSUNG      | O                         | https://www.samsungcard.com/     |
| 06    | 신한         | SHINHAN      | O                         | https://www.shinhancard.com/     |
| 07    | 현대         | HYUNDAI      | O                         | https://www.hyundaicard.com/     |
| 08    | 롯데         | LOTTE        | O                         | https://www.lottecard.co.kr/     |
| 11   | 씨티         | CITI         | O                         | https://www.citibank.co.kr/      |
| 12   | NH채움       | NH           | O                         | https://card.nonghyup.com/       |
| 13   | 수협         | SUHYUP       | O                         | https://www.suhyup-bank.com/     |
| 14   | 신협         | SHINHYUP     | O                         | http://www.cu.co.kr/             |
| 15   | 우리         | WOORI        | O                         | https://pc.wooricard.com/        |
| 16   | 하나         | HANA SK      | O                         | https://www.hanacard.co.kr/      |
| 21   | 광주         | KWANGJU      | O                         | https://pib.kjbank.com/          |
| 22   | 전북         | JEONBUK      | O                         | https://www.jbbank.co.kr/        |
| 23   | 제주         | JEJU         | O                         | http://tamna.jeju.go.kr/         |
| 24   | 산은캐피탈   | KDB Capital  | O                         | https://www.kdbc.co.kr/cardhome  |
| 25   | 해외비자     | VISA         | O                         | https://www.visakorea.com/       |
| 26   | 해외마스터   | MASTER       | O                         | https://www.mastercard.us/       |
| 27   | 해외다이너스 | DINERS       | O                         | https://www.dinersclub.com/      |
| 28   | 해외AMX      | AMEX         | O                         | https://www.americanexpress.com/ |
| 29   | 해외JCB      | JCB          | O                         | https://www.jcb.co.jp/           |
| 31   | SK-OKCashBag | SK-OKCashBag | |https://www.okcashbag.com |                                  
| 32   | 우체국       | Post         | O                         | https://www.epostbank.go.kr/     |
| 33   | 저축은행     | Savings Bank | O                         | http://sbcheck.bccard.com/       |
| 34   | 은련         | UnionPay     | O                         | https://www.unionpayintl.com/    |
| 35   | 새마을금고   | MG           | O                         | https://www.kfcc.co.kr/          |
| 36   | KDB산업      | KDB Bank     | O                         | https://www.kdb.co.kr/           |
| 37   | 카카오뱅크   | Kakao Bank   | O                         | https://www.kakaobank.com/       |
| 38   | 케이뱅크     | KBank        | |https://www.kbanknow.com/ |                                  
| 39   | 페이코포인트 | PAYCO        | O                         | https://www.payco.com/           |
| 40   | 카카오머니   | KAKAO        | O                         | https://www.kakaopay.com/        |
| 41   | SSG머니      | SSG          | O                         | https://www.ssgpay.com/          |
| 42   | 네이버포인트 | NAVER        | O                         | https://www.naverfincorp.com/    |

<br>

## 은행코드
| 코드 |        한글       |                                                             영문 | 가상계좌발급                 | 대표 웹 사이트                       |
|------|:-----------------:|:-----------------------------------------------------------------|:------------------------------:|:--------------------------------------|
| 001    |      한국은행     |                                                    Bank of Korea | |http://www.bok.or.kr/        |                                      
| 002    |      산업은행     |                                                         KDB Bank | |https://www.kdb.co.kr/       |                                      
| 003    |      기업은행     |                                                              IBK | O                            | https://www.ibk.co.kr/               |
| 004    | 국민은행          | Kookmin Bank                                                     | O                            | https://www.kbstar.com/              |
| 005    | 외환은행          | Korea Exchange Bank                                              | O                            | https://www.kebhana.com/             |
| 007    | 수협중앙회        | Suhyup Federation                                                | |https://www.suhyup.co.kr/    | https://www.kebhana.com/             |
| 008    | 수출입은행        | The Export-Import Bank of Korea                                  | |https://www.koreaexim.go.kr/ |                                      |
| 011   | 농협중앙회        | National Agricultural Cooperative Federation                     | O                            | https://www.nonghyup.com/            |
| 012   | 농협회원조합      | National Agricultural Cooperative Federation of Members          | |https://www.nonghyup.com/    | https://www.nonghyup.com/            |
| 020   | 우리은행          | WooriBank                                                        | O                            | https://www.wooribank.com/           |
| 023   | SC은행            | SCBank                                                           | O                            | https://www.standardchartered.co.kr/ |
| 026   | 서울은행          | Seoul Bank                                                       | |https://www.kebhana.com/     
| 027   | 한국씨티은행      | Korea city Bank                                                  | |https://www.citibank.co.kr/  |                                      
| 031   | 대구은행          | Daegu Bank                                                       | O                            | https://www.dgb.co.kr/               |
| 032   | 부산은행          | Busan Bank                                                       | O                            | https://www.busanbank.co.kr/         |
| 034   | 광주은행          | KwangjuBank                                                      | O                            | https://pib.kjbank.com/              |
| 035   | 제주은행          | Jeju Bank                                                        | |https://www.e-jejubank.com/  | 
| 037   | 전북은행          | JeonBuk Bank                                                     | |https://www.jbbank.co.kr/    |                                      
| 039   | 경남은행          | Kyongnam Bank                                                    | |https://www.knbank.co.kr/    |                                      
| 045   | 새마을금고연합회  | KFCC Federation                                                  | |https://www.kfcc.co.kr/      |                                      
| 048   | 신협중앙회        | National Credit Union Federation of Korea                        | |http://www.cu.co.kr/         |                                      
| 050   | 상호저축은행      | Mutual Savings Bank                                              | |https://www.fsb.or.kr/       |                                      
| 071   | 정보통신부 우체국 | Postal Savings for the Ministry of Information and Communication | O                            | https://www.epostbank.go.kr/         |
| 081   | 하나은행          | HanaBank                                                         | O                            | https://www.kebhana.com/             |
| 088   | 신한은행          | ShinhanBank                                                      | O                            | https://www.shinhan.com/             |
| 089   | 케이뱅크          | K Bank                                                           | O                            | https://www.kbanknow.com/            |
| 090   | 카카오뱅크        | Kakao Bank                                                       | |https://www.kakaobank.com/   | 

<br>
  
## API 응답코드

## API response code

| Code | message (korean)                                                      |
|:----:|:----------------------------------------------------------------------|
| 3001 | 카드 결제 성공                                                              |
| 3011 | 카드번호 오류                                                               |
| 3012 | 카드가맹점 정보 미확인                                                          |
| 3013 | 카드 가맹점 개시 안됨                                                          |
| 3014 | 카드가맹점 정보 오류                                                           |
| 3021 | 유효기간 오류                                                               |
| 3022 | 할부개월오류                                                                |
| 3023 | 할부개월 한도 초과                                                            |
| 3024 | 할부 최소금액 오류(50000미만)                                                   |
| 3031 | 무이자할부 카드 아님                                                           |
| 3032 | 무이자할부 불가 개월수                                                          |
| 3033 | 무이자할부 가맹점 아님                                                          |
| 3034 | 무이자할부 구분 미설정                                                          |
| 3041 | 금액 오류(1000원 미만 신용카드 승인 불가)                                            |
| 3051 | 해외카드 미등록 가맹점                                                          |
| 3052 | 통화코드 오류                                                               |
| 3053 | 확인 불가 해외카드                                                            |
| 3054 | 환률전환오류                                                                |
| 3055 | 인증시 달러승인 불가                                                           |
| 3056 | 국내카드 달러승인불가                                                           |
| 3057 | 인증 불가카드                                                               |
| 3061 | 국민카드 인터넷안전결제 적용 가맹점                                                   |
| 3062 | 신용카드 승인번호 오류                                                          |
| 3071 | 매입요청 가맹점 아님                                                           |
| 3072 | 매입요청 TID 정보 불일치                                                       |
| 3073 | 기매입 거래                                                                |
| 3081 | 카드 잔액 값 오류                                                            |
| 3091 | 제휴카드 사용불가 가맹점                                                         |
| 3095 | 카드사 실패 응답                                                             |
| 4000 | 계좌이체 결제 성공                                                            |   
| 4001 | 금결원오류응답                                                               |
| 4002 | 회원사 서비스 불가 은행                                                         |
| 4003 | 출금일자 불일치                                                              |
| 4004 | 출금요청금액 불일치                                                            |
| 4005 | 거래번호(TID) 불일치                                                         |
| 4006 | 회신 정보 불일치                                                             |
| 4007 | 계좌이체 승인번호 오류                                                          |
| 4008 | 은행 시스템 서비스 중단                                                         |
| 4100 | 가상계좌 발급 성공                                                            |
| 4110 | 가상계좌 입금 성공                                                            |
| 4120 | 가상계좌 과오납체크 등록 성공                                                      |
| 4101 | 가상계좌 최대거래금액 초과                                                        |
| 4102 | 가상계좌 입금예정일 오류                                                         |
| 4103 | 가상계좌 입금예정시간 오류                                                        |
| 4104 | 가상계좌 정보 오류                                                            |
| 4105 | 가상계좌 벌크계좌 사용불가.(건별계좌 사용 가맹점)                                          |
| 4106 | 가상계좌 벌크계좌 사용불가.(벌크계좌 사용 가맹점)                                          |
| 4107 | 가상계좌 계좌번호 미입력 오류                                                      |
| 4108 | 가상계좌 Pool 가맹점 미등록 오류                                                  |
| 4109 | 해당 계좌는 입금대기상태(다른 계좌 사용요망)                                             |
| 4111 | 가상계좌 Pool 가맹점 계좌형태 설정 오류                                              |
| 4112 | 벌크계좌의 경우 장바구니 개수를 10개 이내로 제한                                          |
| 4113 | 장바구니 상점주문번호(MOID) 포맷 오류 (MID(10)+YYMMDD(6)+일련번호(4))                   |
| 4114 | 장바구니 상점주문번호(MOID) 포맷 오류 (YYMMDD(6)+일련번호(max_size 14))                 |
| 4115 | 해당 계좌번호는 6개월 이내 재사용 금지                                                |
| 4116 | 가상계좌 계좌번호 채번시도 횟수초과 오류(잠시후 재시도 요망)                                    |
| 4117 | 가상계좌 발급 실패                                                            |
| 4118 | 계좌번호 사용중지 상태                                                          |
| 4119 | 가상계좌 채번내역 미존재 오류                                                      |
| 4121 | 가상계좌 과오납체크 등록 실패                                                      |
| 4122 | 가상계좌 입금요청 금액 불일치 오류                                                   |
| 4123 | 가상계좌 채번취소(환불) 상태이므로 입금처리 실패                                           |
| 4124 | 가상계좌 입금내역 기처리 완료                                                      |
| 4125 | 가상계좌 입금만료 제한 설정오류(기준정보)                                               |
| 4126 | 가상계좌 CPID 설정 오류                                                       |
| 4127 | 가상계좌 CPID 미설정 오류                                                      |
| 4141 | 금액오류(1원 이하 이체불가)                                                      |
| 4142 | 가상계좌 과오납체크 미사용 가맹점                                                    |
| 4143 | 가상계좌 과오납체크 미등록 오류                                                     |
| 4145 | 예금주명 수정이 불가능한 MID                                                     |
| 7001 | 현금영수증 처리 성공                                                           |
| 7002 | 현금영수증 종류오류                                                            |
| 7003 | 현금영수증 중복발급                                                            |
| 7004 | 현금영수증 취소오류                                                            |
| 7005 | 현금영수증 부가가치세 오류                                                        |
| 7006 | 현금영수증 최대개수 초과                                                         |
| 7007 | 현금영수증 요청개수 입력 오류                                                      |
| 7008 | 현금영수증 서브몰 발행 미등록 업체                                                   |
| 7009 | 현금영수증 처리 지불수단이 아닙니다                                                   |
| 7010 | VAN응답실패                                                               |
| 7011 | 현금영수증 미발행 요청입니다                                                       |
| 7012 | 현금영수증 Identity 번호 오류                                                  |
| 7013 | 현금영수증 요청구분 값 오류(1:소득공제, 2:지출증빙)                                       |
| 7014 | 현금영수증 서브몰 사업자번호로 발행시 필수값 누락                                           |
| 7015 | 현금영수증 취소시 원승인번호 또는 원거래일자 누락                                           |
| 7041 | 금액오류(0원 이하 발행불가)                                                      |
| 7042 | 현금영수증 최대금액 초과오류                                                       |
| A000 | 휴대폰결제 처리 성공                                                           |
| A001 | 휴대폰결제 처리 실패                                                           |
| A002 | 필수입력값(거래키) 누락                                                         |
| A003 | 필수입력값(이통사구분) 누락                                                       |
| A004 | 필수입력값(SMS승인번호) 누락                                                     |
| A005 | 필수입력값(업체TID) 누락                                                       |
| A006 | 필수입력값(휴대폰번호) 누락                                                       |
| A041 | 결제금액 오류                                                               |
| A564 | 휴대폰결제ID설정 오류                                                          |
| A565 | 휴대폰결제ID미설정 오류                                                         |
| A566 | 휴대폰결제사 설정 오류                                                          |
| A567 | 상품구분코드 설정 오류                                                          |
| A568 | 서비스구분코드 설정 오류                                                         |
| 0000 | 결제성공                                                                  |
| 1534 | 부분취소 불가능 거래                                                           |
| 1615 | 거래금액 합계오류(공급가액,부가세,봉사료,면세금액 합계)                                       |
| 2000 | DB오류                                                                  | 
| 2011 | CINO미존재                                                               |
| 2012 | 주문번호없음                                                                |
| 2032 | 가맹점주문번호길이이상                                                           |
| 2151 | 거래정지 가맹점                                                              |
| 2152 | 미등록가맹점                                                                |
| 2154 | 제휴사상태미확인                                                              |
| 2156 | 중복등록된거래요청                                                             |
| 2157 | 허용되지않는입력방식                                                            |
| 2158 | 중복등록된입력방식                                                             |
| 2159 | 해당은행장애                                                                |
| 2201 | 기승인존재                                                                 |
| F100 | 빌키가 정상적으로 생성되었습니다.                                                    |
| F101 | 빌링 정보가 정상적으로 삭제되었습니다.                                                 |
| F102 | 인증서 검증 오류                                                             |
| F103 | PKCS7 전자서명 검증 실패                                                      |
| F104 | 인증정보 확인중 오류가 발생하였습니다                                                  |
| F105 | OCSP인증 중 오류가 발생하였습니다                                                  |
| F106 | DB 처리중 오류가 발생하였습니다 (TB_TRANS_REQ)                                     |
| F107 | DB 처리중 오류가 발생하였습니다 (TB_TRANS)                                         |
| F108 | DB 처리중 오류가 발생하였습니다 (TB_TRANS_HISTORY)                                 |
| F109 | DB 처리중 오류가 발생하였습니다 (TB_BILL_MASTER)                                   |
| F110 | 빌키발급 처리중 오류가 발생하였습니다                                                  |
| F111 | PKCS7 전자서명 메시지가 존재하지 않습니다                                             |
| F112 | 유효하지않은 카드번호를 입력하셨습니다 (card_bin 없음)                                    |
| F113 | 본인의 신용카드 확인중 오류가 발생하였습니다                                              |
| F114 | 인증서가 유효하지 않습니다                                                        |
| F115 | 빌키 발급 불가 가맹점입니다.(중지)                                                  |
| F116 | 빌키 발급 불가 가맹점입니다.(해지)                                                  |
| F117 | 빌링 미사용 가맹점입니다                                                         |
| F118 | 해당카드는 사용이 불가능 합니다 타사카드를 이용해주세요                                        |
| F200 | 빌링 요청승인이 정상적으로 이루어졌습니다                                                |
| F201 | 이미 등록된 카드 입니다(빌키발급실패)                                                 |
| C000 | 에스크로배송등록 성공                                                           |
| C002 | 에스크로 가맹점 아님                                                           |
| C003 | 에스크로 거래만 배송등록 가능                                                      |
| C004 | 에스크로결제 신청내역 미존재                                                       |
| C005 | 에스크로배송등록 불가상태                                                         |
| C006 | 거래내역이 존재하지 않음                                                         |
| C007 | 취소된 거래는 배송등록 불가                                                       |
| D000 | 에스크로구매결정 성공                                                           |
| D002 | 에스크로 가맹점 아님                                                           |
| D003 | 에스크로 거래만 구매결정 가능                                                      |
| D004 | 에스크로결제 신청내역 미존재                                                       | 
| D005 | 에스크로구매결정 불가상태                                                         |
| D006 | 거래내역이 존재하지 않음                                                         |
| D007 | 고객고유번호 미입력                                                            |
| D008 | 거래요청내역이 존재하지 않음                                                       |
| D009 | 고객고유번호 검증 실패                                                          |
| D010 | 구매결정 이미 처리됨                                                           |
| D011 | 취소된 거래는 구매결정 불가                                                       |
| E000 | 에스크로구매거절 성공                                                           |
| E002 | 에스크로 가맹점 아님                                                           |
| E003 | 에스크로 거래만 구매거절 가능                                                      |
| E004 | 에스크로결제 신청내역 미존재                                                       |
| E005 | 에스크로구매거절 불가상태                                                         |
| E006 | 거래내역이 존재하지 않음                                                         |
| E007 | 고객고유번호 미입력                                                            |
| E008 | 거래요청내역이 존재하지 않음                                                       |
| E009 | 고객고유번호 검증 실패                                                          |
| E010 | 구매거절 이미 처리됨                                                           |
| E011 | 취소된 거래는 구매거절 불가                                                       |
| 2001 | 취소 성공                                                                 |
| 2211 | 환불 성공 (2001과 함께 취소 성공 처리할 것)                                          |
| 2003 | 취소 실패                                                                 |
| 2010 | 취소 요청금액 0원 이하                                                         |
| 2011 | 취소 금액 불일치                                                             |
| 2012 | 취소 해당거래 없음                                                            |
| 2013 | 취소 완료 거래                                                              |
| 2014 | 취소 불가능 거래                                                             |
| 2015 | 기 취소 요청                                                               |
| 2016 | 취소 기한 초과                                                              |
| 2017 | 취소 불가 회원사                                                             |
| 2018 | 신용카드 매입후 취소 불가능 가맹점                                                   |
| 2019 | 타 회원사 거래 취소 불가                                                        |
| 2020 | 망상 취소 허용시간 초과                                                         |
| 2021 | 매입전취소                                                                 |
| 2022 | 매입후취소                                                                 |
| 2023 | 취소 한도 초과                                                              |
| 2024 | 취소패스워드 불일치                                                            |
| 2025 | 취소패스워드 미 입력                                                           |
| 2026 | 입금액보다 취소금액이 큽니다.                                                      |
| 2027 | 에스크로 거래는 구매 또는 구매거절 시 취소가능                                            |
| 2028 | 부분취소 불가능 가맹점                                                          |
| 2029 | 부분취소 불가능 결제수단                                                         |
| 2030 | 해당결제수단 부분취소 불가                                                        |
| 2031 | 전체금액취소 불가                                                             |
| 2032 | 취소금액이 취소가능금액보다 큼                                                      |
| 2033 | 부분취소 불가능금액 전체취소 이용바람                                                  |
| 2052 | 에스크로 부분취소 불가.                                                         |
| A101 | SIGN DATA 검증에 실패하였습니다                                                 |
| A102 | "타입이 맞지않는 파라미터명 명시" + 은(는) 알 수 없는 TYPE 입니다                            |
| A106 | 날짜 형식이 올바르지 않습니다                                                      |
| A107 | 입금예정일이 지났습니다                                                          |
| A108 | 입금예정일을 오늘 이전 날짜로 설정할 수 없습니다                                           |
| A109 | 요청한 MID의 설정 정보가 없습니다                                                  |
| A110 | 외부 연동결과 실패에 대한 코드(외부 에러메세지 그대로 전달)                                    |
| A111 | 다날 휴대폰 통신 오류입니다                                                       |
| A112 | 비정상적인 경로로 접속되었습니다                                                     |
| A113 | 결제 금액이 최소 금액보다 적습니다                                                   |
| A114 | 상점 MID가 유효하지 않습니다                                                     |
| A115 | TID가 유효하지 않습니다                                                        |
| A116 | 요청 금액이 올바르지 않습니다                                                      |
| A117 | 필수입력항목이 누락되었습니다                                                       |
| A118 | 조회 결과데이터 없음                                                           | 
| A119 | 일반 무이자 이벤트 조회 결과 없음                                                   |
| A120 | 부분 무이자 이벤트 조회 결과 없음                                                   |
| A121 | 정의되지 않은 카드코드 입니다                                                      |
| A122 | 타 상점 거래 처리 불가(MID 불일치)                                                |
| A123 | 거래금액 불일치(인증된 금액과 승인요청 금액 불일치)                                         |
| A124 | 해당 BID가 존재하지 않습니다                                                     |
| A125 | BID가 유효하지 않습니다                                                        |
| A126 | 이미 삭제된 빌키이거나 존재하지 않은 빌키입니다                                            |
| A127 | 주문번호 중복 오류                                                            |
| A128 | 키인 가맹점 아닙니다                                                           | 
| A144 | 가맹점 결과 통보에 실패하였습니다                                                    |
| A145 | 외화 결제는 결제수단 지정하여 이용 가능 합니다                                            |
| A146 | 외화 결제 불가한 결제수단 입니다                                                    |
| A147 | 필드 길이가 초과되었습니다                                                        |
| A148 | 빌링 승인은 별도 API 이용 바랍니다                                                 |
| A149 | 빌링 승인만 가능한 API 입니다.(일반결제는 별도 API이용)                                   |
| A150 | ARS 이용 가맹점이 아닙니다                                                      |
| A201 | PID 생성이 누락되었습니다                                                       |
| A202 | 결과코드 생성이 누락되었습니다                                                      |
| A203 | 결과메시지 생성이 누락되었습니다                                                     |
| A204 | 전문 복호화 오류가 발생하였습니다                                                    |
| A205 | 전문 암호화 오류가 발생하였습니다                                                    |
| A210 | 인증 요청내역이 존재하지 않습니다                                                    |
| A211 | 해쉬값 검증에 실패하였습니다                                                       |
| A212 | 잘못된 데이터 형식입니다                                                         |
| A213 | API 초기화 오류                                                            |
| A214 | 해당하는 카드거래가 없습니다                                                       |
| A215 | 해당하는 핸드폰거래가 없습니다                                                      |
| A216 | 해당하는 계좌이체거래가 없습니다                                                     |
| A217 | 해당하는 가상계좌거래가 없습니다                                                     |
| A218 | 해당하는 전자상품권거래가 없습니다                                                    |
| A219 | 환율정보 설정 오류 입니다                                                        |
| A220 | 설정된 환율 정보가 없습니다                                                       |
| A221 | 노티 수신정보 없음                                                            |
| A222 | 카드코드가 일치하지 않습니다                                                       |
| A223 | billKey 정보를 찾을 수 없습니다                                                 |
| A224 | 허용되지 않은 IP입니다                                                         |
| A225 | TID 중복 오류                                                             |
| A226 | 전문통신 과정에서 오류가 발생하였습니다                                                 |
| A227 | 필드명 중복 발생                                                             |
| A241 | VISA3D 인증을 이용할 수 없는 카드 입니다                                            |
| A242 | 정의되지 않은 지불수단 입니다                                                      | 
| A243 | 주문정보가 존재하지 않습니다                                                       |
| A244 | 주문내역 갱신에 실패하였습니다                                                      |
| A245 | 인증 시간이 초과 되었습니다                                                       |
| A246 | 비정상 과다접속으로 인한 오류입니다                                                   |
| A247 | 정의되지 않은 통화코드 입니다                                                      |
| A248 | 사용할 수 없는 화폐 단위 입니다.(계약정보 확인필요)                                        |
| A249 | 주문정보저장 실패(ORDER_DATA 최대 길이 초과)                                        |
| A250 | 휴대폰번호 변조 오류(요청 번호와 인증완료 번호 상이)                                        |
| A251 | 거래내역이 존재하지 않습니다.                                                      |
| A252 | Signature 생성에 실패하였습니다.                                                |
| A253 | 빌키(BID) 생성에 실패하였습니다.                                                  |
| A254 | 제휴사 응답전문이 유효하지 않습니다.                                                  |
| A255 | 타 상점 빌키(BID) 삭제 불가.                                                   | 
| A256 | ARS인증번호생성실패. 잠시후 재시도 바랍니다.                                            |
| A257 | 주문정보 저장에 실패하였습니다.                                                     |
| A258 | SMS 발송에 실패하였습니다.                                                      |
| A299 | API 지연처리 발생.                                                          |
| A300 | 기준정보 조회오류.                                                            |
| A301 | 가맹점키 조회 오류입니다.                                                        |
| A302 | 기준정보상 필수 설정 정보가 없습니다.                                                 |
| A303 | 기준정보 CPID 설정 정보가 없습니다.                                                |
| A304 | DB테이블 INSERT 오류발생.                                                    |
| A305 | 가맹점번호 기준정보 미설정 오류.                                                    |
| A306 | DB테이블 UPDATE 실패.                                                      |
| A307 | 기준정보 조회 결과 2행 이상 오류                                                   |
| A400 | 대외기관 전문생성에 실패하였습니다.                                                   |
| A401 | 대외계 전문통신 과정에서 오류가 발생하였습니다.                                            |
| A402 | DB트랜잭션 처리에 실패하였습니다.                                                   |
| A403 | 대외기관 응답 전문이 올바르지 않습니다.                                                |
| 9000 | "누락된 필드명" + 필드값이 누락되었습니다.                                             |
| 9001 | 필드 길이가 잘못되었습니다.                                                       |
| 9002 | Try-Catch-Exception:"Exception 내용"                                    |
| S999 | 기타오류가 발생하였습니다.                                                        |
| S001 | 요청템플릿이 존재하지 않습니다.                                                     |
| S002 | 응답템플릿이 존재하지 않습니다.                                                     |
| T001 | 수신메시지 인코딩 중 예외가 발생하였습니다                                               | 
| T002 | 비정상적인 수신 전문입니다                                                        |
| T003 | 수신데이터 파싱 중 예외가 발생하였습니다                                                |
| T004 | 요청 전문의 헤더부 생성 중 오류가 발생하였습니다                                           |
| T005 | 요청 전문의 바디부 생성 중 오류가 발생하였습니다                                           |
| X001 | 서버 도메인명이 잘못 설정되었습니다                                                   |
| X002 | 서버로 소켓 연결 중 오류가 발생하였습니다                                               |
| X003 | 전문 수신 중 오류가 발생하였습니다                                                   |
| X004 | 전문 송신 중 오류가 발생하였습니다                                                   |
| V005 | 지원하지 않는 지불수단입니다                                                       |
| V101 | 암호화 플래그 미설정 오류입니다                                                     |
| V102 | 서비스모드를 설정하지 않았습니다                                                     |
| V103 | 지불수단을 설정하지 않았습니다                                                      |
| V104 | 상품개수 미설정 오류입니다                                                        |
| V201 | 상점ID 미설정 오류입니다                                                        |
| V202 | LicenseKey 미설정 오류입니다                                                  |
| V203 | 통화구분 미설정 오류입니다                                                        |
| V204 | MID 미설정 오류입니다                                                         |
| V205 | MallIP 미설정 오류입니다                                                      |
| V301 | 구매자이름 미설정 오류입니다                                                       |
| V302 | 구매자인증번호 미설정 오류입니다                                                     |
| V303 | 구매자연락처 미설정 오류입니다                                                      |
| V304 | 구매자메일주소 미설정 오류입니다                                                     |
| V401 | 상품명 미설정 오류입니다                                                         |
| V402 | 상품금액 미설정 오류입니다                                                        |
| V501 | 카드형태 미설정 오류입니다                                                        |
| V502 | 카드구분 미설정 오류입니다                                                        |
| V503 | 카드코드 미설정 오류입니다                                                        |
| V504 | 카드번호 미설정 오류입니다                                                        |
| V505 | 카드무이자여부 미설정 오류입니다                                                     |
| V506 | 카드인증구분 미설정 오류입니다                                                      |
| V507 | 카드형태 설정 오류입니다                                                         |
| V508 | 카드형태 허용하지 않는 값을 설정하였습니다                                               |
| V509 | 카드구분 허용하지 않는 값을 설정하였습니다                                               |
| V510 | 유효기간 미설정 오류입니다                                                        |
| V511 | 유효기간 허용하지 않는 값을 설정하였습니다                                               |
| V512 | 유효기간의 월 형태가 잘못 설정되었습니다                                                |
| V513 | 카드 비밀번호 미입력 오류입니다                                                     |
| V601 | 은행코드 미설정 오류입니다                                                        |
| V602 | 금융결제원 암호화 데이터 미설정 오류입니다                                               |
| V701 | 가상계좌입금만료일 미설정 오류입니다                                                   |
| VA01 | 거래KEY 미설정 오류입니다                                                       | 
| VA02 | 이통사구분 미설정 오류입니다                                                       |
| VA03 | SMS승인번호 미설정 오류입니다                                                     |
| VA04 | 업체TID 미설정 오류입니다                                                       |
| VA05 | 휴대폰번호 미설정 오류입니다                                                       |
| VA09 | 고객고유번호(주민번호,사업자번호) 미설정 오류입니다                                          |
| VA10 | ENCODE 업체TID 미설정   오류입니다                                              |
| VB02 | 이통사구분 미설정 오류입니다                                                       |
| VB05 | 휴대폰번호 미설정 오류입니다                                                       |
| VB09 | 고객고유번호(주민번호,사업자번호) 미설정 오류입니다                                          |
| VB10 | 고객 IP 미설정 오류입니다                                                       |
| V801 | 취소금액 미설정 오류입니다                                                        |
| V802 | 취소사유 미설정 오류입니다                                                        |
| V803 | 취소패스워드 미설정 오류입니다                                                      |
| P001 | 클라이언트 아이디가 없습니다.                                                      |
| P002 | 토큰생성을 실패하였습니다.                                                        |
| P003 | 로그추적아이디 생성을 실패하였습니다.                                                  |
| P004 | SID가 생성되지 않았습니다.                                                      |
| P005 | 해당되는 MID 정보가 없습니다.                                                    |
| P006 | 해당되는 GID 정보가 없습니다.                                                    |
| P007 | 필수 파라미터 {param}가 없습니다.                                                |
| P008 | 파라미터 {}:{} 길이가 취소길이{} 보다 작은값 입니다.                                     |
| P009 | 파라미터 {}:{} 길이가 최대길이{} 보다 큰값 입니다.                                      |
| P010 | 파라미터 {key}[{value}]가 숫자형식이 아닙니다.                                      |
| P011 | 파라미터 {key}[{value}]가 boolean형식이 아닙니다.                                 |
| P012 | 파라미터 {key}[{value}]는 {} 값만 허용 합니다.                                    |
| P013 | 파라미터 {}[{}]이 공급가액, 부가세, 봉사료, 비과세급액의 합과 동일하지 않습니다.                     |
| P014 | Mid [{}]에 해당 하는 merchant key 정보가 없습니다.                                |
| P015 | orderid {}가 이미 존재합니다.                                                 |
| P016 | 파라미터로 전달된 MID {}에 대한 사용이 불가 합니다.                                      |
| P017 | 결제금액은 0원 결제가 불가합니다.                                                   |
| P018 | 인증 응답 토큰 데이터가 없습니다.                                                   |
| P019 | 요청된 인증 정보가 없습니다. Token [{}].                                          |
| P020 | 네이버페이 easyPayMethod 데이터 확인이 필요합니다.                                    |
| P021 | 지원 카드가 아닙니다.                                                          |
| P022 | 카드와 할부가 동시에 설정되어야 합니다.                                                |
| P023 | 간편결제는 [{}]와 함께 사용이 불가합니다.                                             |
| P024 | 날짜형식(ISO8601)이 아닙니다.                                                  |
| P025 | 트랜잭션 타입이 일치하지 않습니다.                                                   |
| P026 | 승인서버 요청 에러                                                            |
| P027 | 원격 실패(MCI)                                                            |
| P028 | 일시불 01 에러 (일시불은 00으로 설정)                                              |
| P029 | 해당 지불수단은 카드 지정, 할부 지정이 불가 합니다.                                        |
| P030 | 해당 지불수단은 카드 지정, 할부 지정이 필수 입니다.                                        |
| P031 | 가상계좌 유효시간 지정 오류 입니다.                                                  |
| P032 | 다이렉트 및 간편결제는 에스크로 이용이 불가 합니다.                                         |
| P033 | 간편결제는 다중카드 선택이 불가합니다.                                                 |
| P034 | 요청된 금액과 내역 금액이 일치하지 않습니다.                                             |
| P035 | 망취소 요청 오류 입니다.                                                        |
| P036 | 면세금액이 결제금액을 초과할수 없습니다.                                                |
| P037 | 5만원 미만인 경우 할부 지정이 불가 합니다.                                             |
| P038 | 요청전문 검증오류 입니다.                                                        |
| P039 | 세션키는 필수 값입니다.                                                         |
| P040 | 세션키에 해당하는 주문정보가 존재하지 않습니다.                                            |
| P041 | 세션키 길이가 잘못되었습니다.                                                      |
| P042 | 이미 만료된 세션 아이디 입니다.                                                    |
| P043 | 유효하지 않은 세션 아이디 입니다.                                                   |
| P044 | 승인 상태 수정에 실패하였습니다.                                                    |
| P101 | DB 트랜잭션 실패                                                            |
| P102 | auth history 데이터 추가 실패하였습니다.                                          |
| P103 | log trace 데이터 추가 실패하였습니다.                                             |
| P091 | 결제 요청을 취소하였습니다.                                                       |
| U100 | {0} 필수입력항목이 누락되었습니다.                                                  |
| U101 | BASE64 DECODE 실패                                                      |
| U102 | 사용자 인증정보가 존재하지 않습니다.                                                  |
| U103 | 사용자 인증타입이 맞지 않습니다.                                                    |
| U104 | 사용자 인증에 실패하였습니다.                                                      |
| U105 | 필드 최대길이 초과[max:{0}, realLength:{1}]                                   |
| U106 | 현금영수증 취소는 별도 API 이용 요망                                                |
| U107 | 거래내역이 존재 하지 않습니다.                                                     |
| U108 | 허가되지 않은 요청 입니다.                                                       |
| U109 | 허용되지 않은 요청 입니다.                                                       |
| U110 | 지원하지 않는 지불수단 입니다.                                                     |
| U111 | 조회내용이 없습니다.                                                           |
| U112 | 이미 사용된 OrderId 입니다.                                                   |
| U113 | 빌키 승인 금액 불일치                                                          |
| U114 | 이미 사용된 TID 입니다.                                                       |
| U115 | 삭제 처리된 BID 입니다.                                                       |
| U116 | 사용자 정보가 존재하지 않습니다.                                                    |
| U117 | 사용자 비밀번호가 일치하지 않습니다.                                                  |
| U118 | 이용 불가한 사용자 정보 입니다.                                                    |
| U119 | 지원하지 않는 지불수단입니다.                                                      |
| U120 | TID가 유효하지 않습니다.                                                       |
| U121 | 인증 요청내역이 존재하지 않습니다.                                                   |
| U122 | 취소 해당거래 없음                                                            |
| U123 | 취소금액이 취소가능금액보다 큼.                                                     |
| U124 | 필드 길이가 잘못되었습니다.                                                       |
| U125 | 잘못된 요청 입니다.                                                           |
| U126 | orderId가 존재 하지 않습니다.                                                  |
| U127 | 요청 금액이 올바르지 않습니다.                                                     |
| U128 | 부분취소는 운영 환경에서 이용 가능(샌드박스는 부분취소 미제공)                                   |
| U129 | 잘못된 요청 기간 입니다.                                                        |
| U130 | 허용된 Date형식이 아닙니다.                                                     |
| U131 | 허용된 Data형식이 아닙니다.                                                     |
| U132 | 허용된 옵션 내용이 아닙니다.[{0}]                                                 |
| U301 | ORDER_DATA 최대 길이 초과.                                                  |
| U302 | 응답전문 최대 길이 초과.                                                        |
| U303 | API 지연처리 발생.                                                          |
| U304 | BASIC AUTHENTICATION 실패                                               |
| U305 | BEARER AUTHENTICATION 실패                                              |
| U306 | 전자서명 및 암호화메시지 검증 실패                                                   |
| U307 | 인증정보 확인중 오류가 발생하였습니다.                                                 |
| U308 | Method Not Allowed.                                                   |
| U309 | 발급되지 않은 BID 입니다.                                                      |
| U310 | SIGN DATA 생성에 실패하였습니다.                                                |
| U311 | 인증이 취소되었거나 실패하였습니다. 다시 시도하여 주십시요.                                     |
| U312 | SIGN DATA 검증에 실패하였습니다.                                                |
| U313 | 상점 MID가 유효하지 않습니다.                                                    |
| U314 | 전문 암호화 오류가 발생하였습니다.                                                   |
| U315 | 허용되지 않은 IP 입니다.                                                       |
| U316 | 상점 기준정보가 유효하지 않습니다.                                                   |
| U317 | 인증정보 확인중 오류가 발생하였습니다.                                                 |
| U318 | 취소 금액 불일치                                                             |
| U319 | 요청하신 면세금액이 취소가능한 면세금액을 초과 오류                                          |
| U320 | 페이지를 찾을 수 없습니다.                                                       |
| U321 | 면세금액이 결제금액 보다 큼.                                                      |
| U322 | 세션아이디 생성을 실패 하였습니다.                                                   |
| U323 | 세션아이디 만료 변경이 실패 하였습니다.                                                |
| U324 | 이미 발급된 세션아이디 입니다.                                                     |
| U325 | 이미 만료된 세션아이디 입니다.                                                     |
| U501 | 대외계 전문통신 과정에서 오류가 발생하였습니다.                                            |
| U502 | 요청 금액이 올바르지 않습니다.                                                     |
| U503 | 망취소 요청                                                                |
| U504 | 결제에 실패하였습니다.(망취소 처리)                                                  |
| U506 | DB 테이블 INSERT 오류발생.                                                   |
| U507 | DB 테이블 UPDATE 실패.                                                     |
| U508 | 서버로 소켓 연결 중 오류가 발생하였습니다.                                              |
| U509 | 기준정보 조회 결과 2행 이상 오류                                                   |
| U700 | WEBHOOK 응답전문 최대 길이 초과.                                                |
| C001 | ISP 인증이 취소되었거나 실패하였습니다 다시 시도하여 주십시요                                   |
| C002 | 카드사 인증 실패                                                             |
| I001 | 서버와의 통신에 실패하였습니다 네트워크 환경을 확인하세요                                       |
| I002 | 사용자가 결제를 취소하였습니다                                                      |
| I003 | 인증 성공한 거래로 재요청 되었습니다 결제가 정상적으로 이루어 지지 않았을 경우 가맹점 페이지로 가서 다시 결제하여 주십시요 |
| I004 | 인증 실패한 거래로 재요청 되었습니다 가맹점 페이지로 가서 다시 결제하여 주십시요                         |
| I006 | 화면 구성 처리에 실패하였습니다 가맹점 페이지로 가서 다시 결제하여 주십시요                            |
| I007 | 바로 호출 결제요청 정보를 확인하십시오                                                 |
| M001 | 계좌이체 CPID를 확인해 주세요                                                    |
| M002 | CPID미설정 오류입니다                                                         |
| N001 | 30만원 이상 결제는 지원되지 않습니다                                                 |
| N002 | 주문내역 갱신에 실패하였습니다                                                      |
| N003 | 인증기관 거래설정에 실패하였습니다 가맹점 페이지로 가서 다시 결제하여 주십시요                           |
| S004 | 무이자 설정 정보 오류                                                          |
| S005 | 청구할인 행사 정보 오류                                                         |
| V001 | 가맹점 아이디가 조회 되지 않습니다                                                   |
| V003 | 비정상 과다접속으로 인한 오류입니다 다시 결제 시도하시기 바랍니다                                  |
| V004 | 할부개월 설정 오류                                                            |
| W000 | 정상 처리되었습니다                                                            |
| W001 | 주문번호가 유효하지 않습니다                                                       |
| W002 | TID가 유효하지 않습니다                                                        |
| U332 | 간편결제는 cardCode, cardQuota와 함께 사용이 불가합니다.                              | 
| U333 | 이미 등록된 URL 정보가 있습니다.                                                  | 
| U334 | 웹훅 URL 수정을 실패하였습니다.                                                   | 
| U335 | 웹훅 URL 생성을 실패하였습니다.                                                   |
| U336 | 해당 URL 페이지 요청을 실패하였습니다.                                               |
| U337 | HTTP 상태 코드가 정상(200)이 아닙니다.                                            |
| U338 | 응답 페이지 Body 부분은 OK 문자만 허용됩니다.                                         |

<br>
    
## 더 알아보기
결제 개발을 위해 더 상세한 정보가 필요하다면📌 `공통` 탭의 정보를 활용하고,  
API 개발을 위한 각 인터페이스의 개발 명세가 필요하다면 📚 `문서` 탭의 자료를 확인 해주세요.  
개발이 완료되어 운영에 필요한 정보와 Tip은 ☸️ `운영` 탭의 정보를 통해 확인이 가능 합니다. 

### 📌 공통
개발 전 필요한 `공통`적인 가이드 입니다.  
- [개발 준비](/common/preparations.md) 👉 [회원가입](/common/preparations.md#회원가입) | [API KEY확인](/common/preparations.md#api-key-확인) | [방화벽 정책](common/preparations.md#방화벽-정책) | [IP 보안기능](/common/preparations.md#ip-보안-기능) | [타임아웃 정보](/common/preparations.md#타임아웃-정보)
- [API·JS SDK](/common/api.md) 👉 [URI 목록](/common/api.md#uri-목록) | [JS SDK목록](/common/api.md#js-sdk-목록) | [API KEY](/common/api.md#api-key) | [API·JS SDK인증](/common/api.md#apijs-sdk인증) | [Basic auth](/common/api.md#basic-auth) | [Bearer token](/common/api.md#bearer-token)
- [TEST·샘플코드](/common/test.md) 👉 [샌드박스 TEST](/common/test.md#샌드박스test) | [샌드박스 활용](/common/test.md#샌드박스-활용) | [웹로그 디버깅](/common/test.md#웹로그-디버깅) | [샘플코드](/common/test.md#샘플코드)
- [코드집](/common/code.md) 👉 [HTTP-상태코드](/common/code.md#http-상태코드) | [카드코드](/common/code.md#카드코드) | [은행코드](/common/code.md#은행코드) | [JS SDK 응답코드](/common/code.md#js-sdk-응답코드) | [API 응답코드](/common/code.md#api-응답코드)
  
### 📚 문서
`API 명세`와 `코드`가 포함된 기술문서 입니다.  
- [결제·발급](/api/payment.md#) 👉 [결제창](/api/payment-window-server.md) | [빌링](/api/payment-subscribe.md) | [현금영수증](/api/payment-receipt.md) | [Access token](/api/payment-access-token.md)
- [조회](/api/status.md) 👉 [거래 조회](/api/status-transaction.md) | [약관 조회](/api/status-terms.md) | [카드 이벤트 조회](/api/status-event.md) | [카드 무이자 조회](/api/status-interest.md)
- [취소·환불·망취소](/api/cancel.md) 👉  [취소·환불](/api/cancel.md#취소환불) | [망 취소](/api/cancel.md#망취소)
- [웹훅](/api/hook.md) 👉 [웹훅](/api/hook.md#웹훅)
- [APP](/api/app.md) 👉 [iOS](/api/app-ios.md#ios) | [iOS Swift](/api/app-ios.md#ios-swift-웹뷰web-view개발-가이드) | [iOS Objective-c](/api/app-ios.md#ios-objective-c-웹뷰web-view개발-가이드) | [Android](/api/app-android.md#) | [Android java](/api/app-android.md#android-java-웹뷰web-view개발-가이드) | [Android kotlin](/api/app-android.md#android-kotlin-웹뷰web-view개발-가이드)

### ☸️ 운영
`운영`에 필요한 정보 입니다.  
- [지원환경](/management/user.md) 👉 [개발환경](/management/user.md#개발환경) | [지원 브라우저](/management/user.md#브라우저)
- [오류관리](/management/user.md#오류관리) 👉 [오류관리](/management/user.md#오류관리)
- [개발정보](/management/admin.md) 👉 [기능 요약](/management/admin.md#기능-요약) | [KEY 정보](/management/admin.md#key정보) | [ip보안(ip접근제한)](/management/admin.md#ip보안ip접근-제한) | [웹훅](/management/admin.md#웹훅) | [로그](/management/admin.md#로그)
