/// 바라 부스 매니저 - Firebase Functions 카드 등록 화면
///
/// Firebase Functions를 통해 나이스페이 API로 빌링키를 발급받는 화면입니다.
/// - 서버 사이드에서 안전한 API 호출
/// - SecretKey 보안 관리
/// - 나이스페이 공식 신 버전 API 사용
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../services/firebase_nicepay_service.dart';
import '../../services/nice_pay_service.dart';
import '../../utils/logger_utils.dart';
import '../../utils/toast_utils.dart';
import '../../widgets/app_bar_styles.dart';


/// Firebase Functions 카드 등록 화면
class FirebaseCardRegistrationScreen extends StatefulWidget {
  final String customerName;
  final String customerEmail;
  final String customerTel;

  const FirebaseCardRegistrationScreen({
    super.key,
    required this.customerName,
    required this.customerEmail,
    required this.customerTel,
  });

  @override
  State<FirebaseCardRegistrationScreen> createState() => _FirebaseCardRegistrationScreenState();
}

class _FirebaseCardRegistrationScreenState extends State<FirebaseCardRegistrationScreen> {
  static const String _tag = 'FirebaseCardRegistrationScreen';
  
  final _formKey = GlobalKey<FormState>();
  final FirebaseNicePayService _firebaseService = FirebaseNicePayService();
  
  // 카드 정보 컨트롤러
  final _cardNoController = TextEditingController();
  final _expYearController = TextEditingController();
  final _expMonthController = TextEditingController();
  final _idNoController = TextEditingController();
  final _cardPwController = TextEditingController();
  
  bool _isProcessing = false;

  @override
  void dispose() {
    _cardNoController.dispose();
    _expYearController.dispose();
    _expMonthController.dispose();
    _idNoController.dispose();
    _cardPwController.dispose();
    super.dispose();
  }

  /// 카드 등록 처리
  Future<void> _registerCard() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      LoggerUtils.logInfo('Firebase Functions 카드 등록 시작', tag: _tag);

      final result = await _firebaseService.createBillingKey(
        cardNo: _cardNoController.text.replaceAll('-', ''),
        expYear: _expYearController.text,
        expMonth: _expMonthController.text,
        idNo: _idNoController.text,
        cardPw: _cardPwController.text,
        buyerName: widget.customerName,
        buyerEmail: widget.customerEmail,
        buyerTel: widget.customerTel,
      );

      if (result.success && result.bid != null) {
        // 성공 시 결과 반환
        final niceBillingResult = NiceBillingKeyResult(
          success: true,
          billingKey: result.bid!,
          tid: result.tid!,
          cardInfo: NiceCardInfo(
            cardCode: result.cardCode ?? '',
            cardName: result.cardName ?? '',
            cardCl: '0',
            acquCardCode: result.cardCode ?? '',
            acquCardName: result.cardName ?? '',
          ),
          authDate: result.authDate ?? '',
        );

        if (mounted) {
          Navigator.of(context).pop(niceBillingResult);
        }
      } else {
        ToastUtils.showError(context, result.errorMessage ?? 'Firebase Functions 빌링키 발급에 실패했습니다.');
      }
    } catch (e) {
      LoggerUtils.logError('Firebase Functions 카드 등록 중 오류', tag: _tag, error: e);
      ToastUtils.showError(context, 'Firebase Functions 카드 등록 중 오류가 발생했습니다.');
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Firebase Functions 카드 등록',
          style: AppBarStyles.of(context),
        ),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 안내 정보
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.orange.shade50,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.orange.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.cloud,
                            color: Colors.orange.shade700,
                            size: 24,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Firebase Functions 방식',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.orange.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '• 서버 사이드에서 안전한 API 호출\n'
                        '• SecretKey 보안 관리\n'
                        '• 나이스페이 공식 신 버전 API 사용\n'
                        '• 실제 카드 정보로 빌링키 발급',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.orange.shade700,
                          height: 1.4,
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 24),

                // 카드번호
                TextFormField(
                  controller: _cardNoController,
                  decoration: const InputDecoration(
                    labelText: '카드번호',
                    hintText: '1234-5678-9012-3456',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(16),
                    _CardNumberFormatter(),
                  ],
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '카드번호를 입력해주세요';
                    }
                    final digits = value.replaceAll('-', '');
                    if (digits.length != 16) {
                      return '올바른 카드번호를 입력해주세요 (16자리)';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 16),

                // 유효기간
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _expMonthController,
                        decoration: const InputDecoration(
                          labelText: '유효기간 (월)',
                          hintText: 'MM',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          LengthLimitingTextInputFormatter(2),
                        ],
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return '월을 입력해주세요';
                          }
                          final month = int.tryParse(value);
                          if (month == null || month < 1 || month > 12) {
                            return '올바른 월을 입력해주세요 (01-12)';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _expYearController,
                        decoration: const InputDecoration(
                          labelText: '유효기간 (년)',
                          hintText: 'YY',
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          LengthLimitingTextInputFormatter(2),
                        ],
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return '년을 입력해주세요';
                          }
                          if (value.length != 2) {
                            return '2자리 년도를 입력해주세요';
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),

                // 생년월일
                TextFormField(
                  controller: _idNoController,
                  decoration: const InputDecoration(
                    labelText: '생년월일',
                    hintText: 'YYMMDD (예: 800101)',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(6),
                  ],
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '생년월일을 입력해주세요';
                    }
                    if (value.length != 6) {
                      return '생년월일 6자리를 입력해주세요 (YYMMDD)';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 16),

                // 카드 비밀번호
                TextFormField(
                  controller: _cardPwController,
                  decoration: const InputDecoration(
                    labelText: '카드 비밀번호 앞 2자리',
                    hintText: '**',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  obscureText: true,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(2),
                  ],
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '카드 비밀번호 앞 2자리를 입력해주세요';
                    }
                    if (value.length != 2) {
                      return '카드 비밀번호 앞 2자리를 입력해주세요';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 32),
                
                // 등록 버튼
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isProcessing ? null : _registerCard,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: _isProcessing
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Text(
                            'Firebase Functions로 카드 등록',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 카드번호 포맷터 (1234-5678-9012-3456)
class _CardNumberFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text.replaceAll('-', '');
    final buffer = StringBuffer();
    
    for (int i = 0; i < text.length; i++) {
      if (i > 0 && i % 4 == 0) {
        buffer.write('-');
      }
      buffer.write(text[i]);
    }
    
    final formatted = buffer.toString();
    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}
