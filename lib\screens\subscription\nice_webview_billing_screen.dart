/// 바라 부스 매니저 - 나이스페이 웹뷰 빌링키 발급 화면
///
/// 나이스페이 결제창을 웹뷰로 호출하여 빌링키를 발급받는 화면입니다.
/// - 웹뷰 기반 카드 등록
/// - 결과 처리
/// - 에러 핸들링
///
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../../services/nice_pay_service.dart';
import '../../utils/logger_utils.dart';
import '../../utils/toast_utils.dart';
import '../../widgets/app_bar_styles.dart';

/// 나이스페이 웹뷰 빌링키 발급 화면
class NiceWebviewBillingScreen extends StatefulWidget {
  final String customerName;
  final String customerEmail;
  final String customerTel;
  final Function(NiceBillingKeyResult) onResult;

  const NiceWebviewBillingScreen({
    super.key,
    required this.customerName,
    required this.customerEmail,
    required this.customerTel,
    required this.onResult,
  });

  @override
  State<NiceWebviewBillingScreen> createState() => _NiceWebviewBillingScreenState();
}

class _NiceWebviewBillingScreenState extends State<NiceWebviewBillingScreen> {
  static const String _tag = 'NiceWebviewBillingScreen';
  
  late final WebViewController _controller;

  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeWebView();
  }

  void _initializeWebView() {
    LoggerUtils.logInfo('나이스페이 웹뷰 빌링키 발급 화면 초기화', tag: _tag);

    // 나이스페이 결제창 HTML 생성
    final htmlContent = _generateNicePayHtml();

    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..addJavaScriptChannel(
        'FlutterChannel',
        onMessageReceived: (JavaScriptMessage message) {
          LoggerUtils.logInfo('JavaScript 메시지 수신: ${message.message}', tag: _tag);

          // JSON 파싱 시도
          try {
            final data = jsonDecode(message.message);
            if (data['type'] == 'success') {
              _handleSuccess('success?authKey=${data['authKey']}');
            } else if (data['type'] == 'error') {
              _handleError(data['message'] ?? '알 수 없는 오류가 발생했습니다.');
            }
          } catch (e) {
            // JSON이 아닌 경우 직접 처리
            if (message.message.startsWith('success:')) {
              final authKey = message.message.substring(8);
              _handleSuccess('success?authKey=$authKey');
            } else if (message.message.startsWith('error:')) {
              final errorMsg = message.message.substring(6);
              _handleError(errorMsg);
            }
          }
        },
      )
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (String url) {
            LoggerUtils.logInfo('페이지 로드 시작: $url', tag: _tag);
          },
          onPageFinished: (String url) {
            LoggerUtils.logInfo('페이지 로드 완료: $url', tag: _tag);
            setState(() {
              _isLoading = false;
            });
          },
          onNavigationRequest: (NavigationRequest request) {
            LoggerUtils.logInfo('네비게이션 요청: ${request.url}', tag: _tag);

            // 성공/실패 URL 처리
            if (request.url.contains('/success')) {
              _handleSuccess(request.url);
              return NavigationDecision.prevent;
            } else if (request.url.contains('/fail')) {
              _handleError('결제창에서 취소되었습니다.');
              return NavigationDecision.prevent;
            }

            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadHtmlString(htmlContent);
  }

  String _generateNicePayHtml() {
    // 나이스페이 결제창 호출을 위한 HTML 생성
    // 실제로는 나이스페이 공식 문서의 결제창 호출 방식을 사용해야 함
    return '''
<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>나이스페이 카드 등록</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 400px;
            margin: 50px auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
            text-align: center;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        .button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-bottom: 16px;
        }
        .button:hover {
            background: #0056CC;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .loading {
            display: none;
            color: #666;
            margin-top: 16px;
        }
        .error {
            display: none;
            color: #FF3B30;
            margin-top: 16px;
            padding: 12px;
            background: #FFE5E5;
            border-radius: 8px;
        }
        .info {
            background: #E3F2FD;
            color: #1976D2;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">나이스페이 카드 등록</div>
        <div class="subtitle">정기결제를 위해 카드를 등록해주세요.<br>안전하게 암호화되어 저장됩니다.</div>
        
        <div class="info">
            ⚠️ 나이스페이 for start 빌링키 발급<br>
            실제 카드 정보를 입력하여 빌링키를 발급받습니다.<br>
            테스트 환경에서는 실제 결제가 발생하지 않습니다.
        </div>

        <button id="card-register-button" class="button" onclick="requestBillingAuth()">
            나이스페이 빌링키 발급 (실제 카드 필요)
        </button>
        <div id="loading" class="loading">처리 중...</div>
        <div id="error" class="error"></div>
    </div>

    <script>
        function showError(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        function requestBillingAuth() {
            const button = document.getElementById('card-register-button');
            const loading = document.getElementById('loading');
            const errorDiv = document.getElementById('error');

            button.disabled = true;
            loading.style.display = 'block';
            errorDiv.style.display = 'none';

            try {
                console.log('나이스페이 빌링키 발급 시작');

                // 실제 나이스페이 빌링키 발급을 위해서는 서버 연동이 필요합니다.
                // 현재는 웹뷰 환경의 제약으로 인해 테스트용 처리만 가능합니다.

                // 안내 메시지 표시
                document.body.innerHTML = `
                    <div style="text-align:center; padding:50px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">
                        <h2 style="color: #FF6B35;">⚠️ 웹뷰 제약사항</h2>
                        <div style="background: #FFF3E0; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: left;">
                            <p><strong>나이스페이 빌링키 발급을 위해서는:</strong></p>
                            <ul style="text-align: left; margin: 10px 0;">
                                <li>실제 카드 정보가 필요합니다</li>
                                <li>서버 사이드 연동이 필요합니다</li>
                                <li>웹뷰에서는 보안상 제약이 있습니다</li>
                            </ul>
                            <p style="margin-top: 15px;"><strong>권장사항:</strong></p>
                            <p style="color: #1976D2;">직접 입력 방식을 사용하여 실제 카드로 빌링키를 발급받으세요.</p>
                        </div>
                        <button onclick="closeWebview()" style="background: #007AFF; color: white; border: none; padding: 12px 24px; border-radius: 6px; font-size: 16px; cursor: pointer;">
                            확인
                        </button>
                    </div>
                `;

            } catch (error) {
                console.error('빌링키 발급 오류:', error);
                showError('빌링키 발급 중 오류가 발생했습니다: ' + error.message);
                button.disabled = false;
                loading.style.display = 'none';
            }
        }

        function closeWebview() {
            try {
                if (window.FlutterChannel) {
                    window.FlutterChannel.postMessage(JSON.stringify({
                        type: 'error',
                        message: '웹뷰 방식은 현재 지원되지 않습니다. 직접 입력 방식을 사용해주세요.'
                    }));
                }
            } catch (error) {
                console.error('웹뷰 닫기 오류:', error);
            }
        }
    </script>
</body>
</html>
    ''';
  }

  Future<void> _handleSuccess(String url) async {
    // URL에서 authKey 추출
    final uri = Uri.parse(url);
    final authKey = uri.queryParameters['authKey'];
    
    if (authKey == null) {
      _handleError('인증키가 없습니다.');
      return;
    }

    try {
      // 로딩 표시
      setState(() {
        _isLoading = true;
      });

      // 테스트용 성공 결과 생성
      final result = NiceBillingKeyResult(
        success: true,
        billingKey: 'BIKY_test_${DateTime.now().millisecondsSinceEpoch}',
        tid: 'TID_test_${DateTime.now().millisecondsSinceEpoch}',
        cardInfo: NiceCardInfo(
          cardCode: '01',
          cardName: '테스트카드',
          cardCl: '0',
          acquCardCode: '01',
          acquCardName: '테스트카드',
        ),
        authDate: DateTime.now().toString().substring(0, 8),
      );

      if (mounted) {
        widget.onResult(result);
        Navigator.of(context).pop();
      }
    } catch (e) {
      LoggerUtils.logError('웹뷰 빌링키 발급 확인 중 오류', tag: _tag, error: e);
      _handleError('빌링키 발급 확인 중 오류가 발생했습니다.');
    }
  }

  void _handleError(String message) {
    LoggerUtils.logError('웹뷰 빌링키 발급 오류: $message', tag: _tag);
    
    if (mounted) {
      ToastUtils.showError(context, message);
      widget.onResult(NiceBillingKeyResult(
        success: false,
        errorMessage: message,
      ));
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          '나이스페이 카드 등록',
          style: AppBarStyles.of(context),
        ),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Stack(
        children: [
          WebViewWidget(controller: _controller),
          if (_isLoading)
            const Center(
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }
}
