/// 바라 부스 매니저 - 카드 등록 화면 (나이스 페이)
///
/// 나이스 페이 빌링키 발급을 위한 카드 정보 입력 화면입니다.
/// 
/// 작성자: Blue
/// 버전: 1.0.0
/// 최종 업데이트: 2025년 8월
library;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../services/nice_pay_service.dart';
import '../../utils/logger_utils.dart';
import '../../utils/toast_utils.dart';
import '../../widgets/app_bar_styles.dart';

/// 카드 등록 화면
class CardRegistrationScreen extends StatefulWidget {
  final String customerName;
  final String customerEmail;
  final String customerTel;
  final Function(NiceBillingKeyResult) onResult;

  const CardRegistrationScreen({
    super.key,
    required this.customerName,
    required this.customerEmail,
    required this.customerTel,
    required this.onResult,
  });

  @override
  State<CardRegistrationScreen> createState() => _CardRegistrationScreenState();
}

class _CardRegistrationScreenState extends State<CardRegistrationScreen> {
  static const String _tag = 'CardRegistrationScreen';
  
  final NicePayService _nicePayService = NicePayService();
  final _formKey = GlobalKey<FormState>();
  
  // 폼 컨트롤러들
  final _cardNoController = TextEditingController();
  final _expYearController = TextEditingController();
  final _expMonthController = TextEditingController();
  final _idNoController = TextEditingController();
  final _cardPwController = TextEditingController();
  
  bool _isProcessing = false;

  @override
  void dispose() {
    _cardNoController.dispose();
    _expYearController.dispose();
    _expMonthController.dispose();
    _idNoController.dispose();
    _cardPwController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Builder(builder: (ctx) => Text('카드 등록', style: AppBarStyles.of(ctx))),
        centerTitle: true,
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 안내 메시지
                _buildNoticeCard(),
                const SizedBox(height: 24),
                
                // 카드 정보 입력
                _buildCardInfoSection(),
                const SizedBox(height: 32),
                
                // 등록 버튼
                _buildRegisterButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNoticeCard() {
    return Card(
      color: Colors.blue.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue.shade700),
                const SizedBox(width: 8),
                Text(
                  '카드 등록 안내',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '• 카드 정보는 안전하게 암호화되어 저장됩니다\n'
              '• 테스트 환경에서는 실제 결제가 발생하지 않습니다\n'
              '• 테스트 카드 번호:\n'
              '  - 5570-0000-0000-0001 (BC카드)\n'
              '  - 5555-5555-5555-4444 (마스터카드)\n'
              '  - 4000-0000-0000-0002 (비자카드)',
              style: TextStyle(
                fontSize: 14,
                color: Colors.blue.shade700,
                height: 1.4,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCardInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '카드 정보',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 16),
        
        // 카드번호
        TextFormField(
          controller: _cardNoController,
          decoration: const InputDecoration(
            labelText: '카드번호',
            hintText: '1234-5678-9012-3456',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.credit_card),
          ),
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(16),
            _CardNumberFormatter(),
          ],
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '카드번호를 입력해주세요';
            }
            final digits = value.replaceAll('-', '');
            if (digits.length != 16) {
              return '올바른 카드번호를 입력해주세요 (16자리)';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        
        // 유효기간
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _expMonthController,
                decoration: const InputDecoration(
                  labelText: '월 (MM)',
                  hintText: '12',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(2),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '월을 입력해주세요';
                  }
                  final month = int.tryParse(value);
                  if (month == null || month < 1 || month > 12) {
                    return '올바른 월을 입력해주세요';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                controller: _expYearController,
                decoration: const InputDecoration(
                  labelText: '년 (YY)',
                  hintText: '25',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                  LengthLimitingTextInputFormatter(2),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '년도를 입력해주세요';
                  }
                  if (value.length != 2) {
                    return '2자리 년도를 입력해주세요';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        
        // 생년월일/사업자번호
        TextFormField(
          controller: _idNoController,
          decoration: const InputDecoration(
            labelText: '생년월일 (YYMMDD) 또는 사업자번호',
            hintText: '800101 또는 1234567890',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.person),
          ),
          keyboardType: TextInputType.number,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(10),
          ],
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '생년월일 또는 사업자번호를 입력해주세요';
            }
            if (value.length != 6 && value.length != 10) {
              return '생년월일 6자리 또는 사업자번호 10자리를 입력해주세요';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        
        // 카드 비밀번호
        TextFormField(
          controller: _cardPwController,
          decoration: const InputDecoration(
            labelText: '카드 비밀번호 앞 2자리',
            hintText: '12',
            border: OutlineInputBorder(),
            prefixIcon: Icon(Icons.lock),
          ),
          keyboardType: TextInputType.number,
          obscureText: true,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(2),
          ],
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '카드 비밀번호 앞 2자리를 입력해주세요';
            }
            if (value.length != 2) {
              return '2자리를 입력해주세요';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildRegisterButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _isProcessing ? null : _registerCard,
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _isProcessing
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Text(
                '카드 등록',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }

  Future<void> _registerCard() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      LoggerUtils.logInfo('카드 등록 시작', tag: _tag);

      final result = await _nicePayService.issueBillingKey(
        cardNo: _cardNoController.text.replaceAll('-', ''),
        expYear: _expYearController.text,
        expMonth: _expMonthController.text,
        idNo: _idNoController.text,
        cardPw: _cardPwController.text,
        customerName: widget.customerName,
        customerEmail: widget.customerEmail,
        customerTel: widget.customerTel,
      );

      if (mounted) {
        widget.onResult(result);
        Navigator.of(context).pop(result);
      }
    } catch (e) {
      LoggerUtils.logError('카드 등록 중 오류', tag: _tag, error: e);
      if (mounted) {
        ToastUtils.showError(context, '카드 등록 중 오류가 발생했습니다.');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }
}

/// 카드번호 포맷터 (1234-5678-9012-3456)
class _CardNumberFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text.replaceAll('-', '');
    final buffer = StringBuffer();
    
    for (int i = 0; i < text.length; i++) {
      if (i > 0 && i % 4 == 0) {
        buffer.write('-');
      }
      buffer.write(text[i]);
    }
    
    final formatted = buffer.toString();
    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}
