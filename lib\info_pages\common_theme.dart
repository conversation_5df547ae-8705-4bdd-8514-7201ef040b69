import 'package:flutter/material.dart';

class InfoTheme {
  // 컬러 시스템
  static const Color primary = Color(0xFFE09A74);
  static const Color primaryDark = Color(0xFFD08052);
  static const Color secondary = Color(0xFF6366F1);
  static const Color accent = Color(0xFF10B981);

  // 텍스트 컬러
  static const Color textPrimary = Color(0xFF1F2937);
  static const Color textSecondary = Color(0xFF6B7280);
  static const Color textMuted = Color(0xFF9CA3AF);

  // 배경 컬러
  static const Color background = Color(0xFFFAFAFA);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF9FAFB);

  // 보더 컬러
  static const Color border = Color(0xFFE5E7EB);
  static const Color borderLight = Color(0xFFF3F4F6);

  static ThemeData theme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: primary,
        brightness: Brightness.light,
        surface: surface,
        background: background,
      ),
      scaffoldBackgroundColor: background,
      fontFamily: 'Pretendard',
      textTheme: const TextTheme(
        displayLarge: TextStyle(fontSize: 48, fontWeight: FontWeight.w800, color: textPrimary, height: 1.1),
        displayMedium: TextStyle(fontSize: 36, fontWeight: FontWeight.w700, color: textPrimary, height: 1.2),
        headlineLarge: TextStyle(fontSize: 32, fontWeight: FontWeight.w700, color: textPrimary, height: 1.2),
        headlineMedium: TextStyle(fontSize: 28, fontWeight: FontWeight.w600, color: textPrimary, height: 1.3),
        headlineSmall: TextStyle(fontSize: 24, fontWeight: FontWeight.w600, color: textPrimary, height: 1.3),
        titleLarge: TextStyle(fontSize: 20, fontWeight: FontWeight.w600, color: textPrimary, height: 1.4),
        titleMedium: TextStyle(fontSize: 18, fontWeight: FontWeight.w500, color: textPrimary, height: 1.4),
        bodyLarge: TextStyle(fontSize: 16, fontWeight: FontWeight.w400, color: textSecondary, height: 1.6),
        bodyMedium: TextStyle(fontSize: 14, fontWeight: FontWeight.w400, color: textSecondary, height: 1.5),
        bodySmall: TextStyle(fontSize: 12, fontWeight: FontWeight.w400, color: textMuted, height: 1.4),
      ),
    );
  }

  // 그라데이션
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient heroGradient = LinearGradient(
    colors: [Color(0xFFF8FAFC), Color(0xFFE2E8F0)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  // 그림자
  static const List<BoxShadow> cardShadow = [
    BoxShadow(
      color: Color(0x0A000000),
      blurRadius: 10,
      offset: Offset(0, 4),
    ),
    BoxShadow(
      color: Color(0x05000000),
      blurRadius: 20,
      offset: Offset(0, 8),
    ),
  ];

  static const List<BoxShadow> buttonShadow = [
    BoxShadow(
      color: Color(0x15E09A74),
      blurRadius: 12,
      offset: Offset(0, 4),
    ),
  ];
}

class CompanyFooter extends StatelessWidget {
  const CompanyFooter({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 40),
      decoration: const BoxDecoration(
        color: InfoTheme.surface,
        border: Border(top: BorderSide(color: InfoTheme.border)),
      ),
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 1200),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 회사 로고/이름
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      gradient: InfoTheme.primaryGradient,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(Icons.storefront, color: Colors.white, size: 20),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    '바라 부스 매니저',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w700,
                      color: InfoTheme.textPrimary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // 회사 정보
              Wrap(
                spacing: 16,
                runSpacing: 8,
                children: const [
                  _FooterInfoItem('상호명', '바라부스매니저'),
                  _FooterInfoItem('대표', '권태영'),
                  _FooterInfoItem('전화', '010-2391-4308'),
                  _FooterInfoItem('이메일', '<EMAIL>'),
                ],
              ),
              const SizedBox(height: 16),

              Wrap(
                spacing: 16,
                runSpacing: 8,
                children: const [
                  _FooterInfoItem('주소', '서울 강서구 마곡동 마곡중앙로 36 1504동 1301호'),
                  _FooterInfoItem('사업자등록번호', '184-53-01069'),
                  _FooterInfoItem('통신판매업신고', '제2021-서울강서-2557호'),
                  _FooterInfoItem('개인정보관리책임자', '권태영'),
                ],
              ),

              const SizedBox(height: 24),
              const Divider(color: InfoTheme.border),
              const SizedBox(height: 16),

              const Text(
                '© 2025 바라부스매니저. All rights reserved.',
                style: TextStyle(
                  fontSize: 12,
                  color: InfoTheme.textMuted,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _FooterInfoItem extends StatelessWidget {
  final String label;
  final String value;

  const _FooterInfoItem(this.label, this.value);

  @override
  Widget build(BuildContext context) {
    return RichText(
      text: TextSpan(
        style: const TextStyle(fontSize: 12, color: InfoTheme.textSecondary),
        children: [
          TextSpan(
            text: '$label : ',
            style: const TextStyle(fontWeight: FontWeight.w600),
          ),
          TextSpan(text: value),
        ],
      ),
    );
  }
}

